!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("react"),require("@mui/material"),require("@emotion/styled"),require("@emotion/react")):"function"==typeof define&&define.amd?define(["exports","react","@mui/material","@emotion/styled","@emotion/react"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).InsightsAppMFE={},e.<PERSON>,e.MaterialUI,e.EmotionStyled)}(this,(function(e,r,t,n){"use strict";function o(e){const r=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e)for(const t in e)if("default"!==t){const n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:()=>e[t]})}return r.default=e,Object.freeze(r)}const i=o(r);function a(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var s,c={exports:{}},l={};var u,p,f={};
/**
   * @license React
   * react-jsx-runtime.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */function d(){return u||(u=1,"production"!==process.env.NODE_ENV&&function(){function e(r){if(null==r)return null;if("function"==typeof r)return r.$$typeof===C?null:r.displayName||r.name||null;if("string"==typeof r)return r;switch(r){case m:return"Fragment";case g:return"Profiler";case y:return"StrictMode";case S:return"Suspense";case w:return"SuspenseList";case k:return"Activity"}if("object"==typeof r)switch("number"==typeof r.tag&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),r.$$typeof){case d:return"Portal";case b:return(r.displayName||"Context")+".Provider";case h:return(r._context.displayName||"Context")+".Consumer";case v:var t=r.render;return(r=r.displayName)||(r=""!==(r=t.displayName||t.name||"")?"ForwardRef("+r+")":"ForwardRef"),r;case $:return null!==(t=r.displayName||null)?t:e(r.type)||"Memo";case x:t=r._payload,r=r._init;try{return e(r(t))}catch(n){}}return null}function t(e){return""+e}function n(e){try{t(e);var r=!1}catch(i){r=!0}if(r){var n=(r=console).error,o="function"==typeof Symbol&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object";return n.call(r,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",o),t(e)}}function o(r){if(r===m)return"<>";if("object"==typeof r&&null!==r&&r.$$typeof===x)return"<...>";try{var t=e(r);return t?"<"+t+">":"<...>"}catch(n){return"<...>"}}function i(){return Error("react-stack-top-frame")}function a(){var r=e(this.type);return _[r]||(_[r]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),void 0!==(r=this.props.ref)?r:null}function s(r,t,o,i,s,u,f,d){var m,y=t.children;if(void 0!==y)if(i)if(T(y)){for(i=0;i<y.length;i++)c(y[i]);Object.freeze&&Object.freeze(y)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else c(y);if(E.call(t,"key")){y=e(r);var g=Object.keys(t).filter((function(e){return"key"!==e}));i=0<g.length?"{key: someKey, "+g.join(": ..., ")+": ...}":"{key: someKey}",I[y+i]||(g=0<g.length?"{"+g.join(": ..., ")+": ...}":"{}",console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />',i,y,g,y),I[y+i]=!0)}if(y=null,void 0!==o&&(n(o),y=""+o),function(e){if(E.call(e,"key")){var r=Object.getOwnPropertyDescriptor(e,"key").get;if(r&&r.isReactWarning)return!1}return void 0!==e.key}(t)&&(n(t.key),y=""+t.key),"key"in t)for(var h in o={},t)"key"!==h&&(o[h]=t[h]);else o=t;return y&&function(e,r){function t(){l||(l=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",r))}t.isReactWarning=!0,Object.defineProperty(e,"key",{get:t,configurable:!0})}(o,"function"==typeof r?r.displayName||r.name||"Unknown":r),function(e,r,t,n,o,i,s,c){return t=i.ref,e={$$typeof:p,type:e,key:r,props:i,_owner:o},null!==(void 0!==t?t:null)?Object.defineProperty(e,"ref",{enumerable:!1,get:a}):Object.defineProperty(e,"ref",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:s}),Object.defineProperty(e,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:c}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}(r,y,u,0,null===(m=O.A)?null:m.getOwner(),o,f,d)}function c(e){"object"==typeof e&&null!==e&&e.$$typeof===p&&e._store&&(e._store.validated=1)}var l,u=r,p=Symbol.for("react.transitional.element"),d=Symbol.for("react.portal"),m=Symbol.for("react.fragment"),y=Symbol.for("react.strict_mode"),g=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),b=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),w=Symbol.for("react.suspense_list"),$=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),k=Symbol.for("react.activity"),C=Symbol.for("react.client.reference"),O=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,E=Object.prototype.hasOwnProperty,T=Array.isArray,A=console.createTask?console.createTask:function(){return null},_={},N=(u={"react-stack-bottom-frame":function(e){return e()}})["react-stack-bottom-frame"].bind(u,i)(),j=A(o(i)),I={};f.Fragment=m,f.jsx=function(e,r,t,n,i){var a=1e4>O.recentlyCreatedOwnerStacks++;return s(e,r,t,!1,0,i,a?Error("react-stack-top-frame"):N,a?A(o(e)):j)},f.jsxs=function(e,r,t,n,i){var a=1e4>O.recentlyCreatedOwnerStacks++;return s(e,r,t,!0,0,i,a?Error("react-stack-top-frame"):N,a?A(o(e)):j)}}()),f}var m,y=(p||(p=1,"production"===process.env.NODE_ENV?c.exports=function(){if(s)return l;s=1;var e=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function t(r,t,n){var o=null;if(void 0!==n&&(o=""+n),void 0!==t.key&&(o=""+t.key),"key"in t)for(var i in n={},t)"key"!==i&&(n[i]=t[i]);else n=t;return t=n.ref,{$$typeof:e,type:r,key:o,ref:void 0!==t?t:null,props:n}}return l.Fragment=r,l.jsx=t,l.jsxs=t,l}():c.exports=d()),c.exports),g={exports:{}},h={};var b,v,S={};
/**
   * @license React
   * react-compiler-runtime.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */var w,$=(v||(v=1,"production"===process.env.NODE_ENV?g.exports=function(){if(m)return h;m=1;var e=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;return h.c=function(r){return e.H.useMemoCache(r)},h}():g.exports=(b||(b=1,"production"!==process.env.NODE_ENV&&(w=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,S.c=function(e){var r=w.H;return null===r&&console.error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem."),r.useMemoCache(e)})),S)),g.exports);function x(e,...r){const t=new URL(`https://mui.com/production-error/?code=${e}`);return r.forEach((e=>t.searchParams.append("args[]",e))),`Minified MUI error #${e}; visit ${t} for the full message.`}var k={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function C(e){var r=Object.create(null);return function(t){return void 0===r[t]&&(r[t]=e(t)),r[t]}}var O=/[A-Z]|^ms/g,E=/_EMO_([^_]+?)_([^]*?)_EMO_/g,T=function(e){return 45===e.charCodeAt(1)},A=function(e){return null!=e&&"boolean"!=typeof e},_=C((function(e){return T(e)?e:e.replace(O,"-$&").toLowerCase()})),N=function(e,r){switch(e){case"animation":case"animationName":if("string"==typeof r)return r.replace(E,(function(e,r,t){return I={name:r,styles:t,next:I},r}))}return 1===k[e]||T(e)||"number"!=typeof r||0===r?r:r+"px"};function j(e,r,t){if(null==t)return"";var n=t;if(void 0!==n.__emotion_styles)return n;switch(typeof t){case"boolean":return"";case"object":var o=t;if(1===o.anim)return I={name:o.name,styles:o.styles,next:I},o.name;var i=t;if(void 0!==i.styles){var a=i.next;if(void 0!==a)for(;void 0!==a;)I={name:a.name,styles:a.styles,next:I},a=a.next;return i.styles+";"}return function(e,r,t){var n="";if(Array.isArray(t))for(var o=0;o<t.length;o++)n+=j(e,r,t[o])+";";else for(var i in t){var a=t[i];if("object"!=typeof a){var s=a;A(s)&&(n+=_(i)+":"+N(i,s)+";")}else if(Array.isArray(a)&&"string"==typeof a[0]&&null==r)for(var c=0;c<a.length;c++)A(a[c])&&(n+=_(i)+":"+N(i,a[c])+";");else{var l=j(e,r,a);switch(i){case"animation":case"animationName":n+=_(i)+":"+l+";";break;default:n+=i+"{"+l+"}"}}}return n}(e,r,t)}return t}var I,P=/label:\s*([^\s;{]+)\s*(;|$)/g;var R,M={exports:{}},B={exports:{}},D={};var V,F,W,U,L,z,G,K,H,Y,q,J,X,Q,Z,ee={};
/** @license React v16.13.1
   * react-is.development.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */function re(){return F||(F=1,"production"===process.env.NODE_ENV?B.exports=function(){if(R)return D;R=1;var e="function"==typeof Symbol&&Symbol.for,r=e?Symbol.for("react.element"):60103,t=e?Symbol.for("react.portal"):60106,n=e?Symbol.for("react.fragment"):60107,o=e?Symbol.for("react.strict_mode"):60108,i=e?Symbol.for("react.profiler"):60114,a=e?Symbol.for("react.provider"):60109,s=e?Symbol.for("react.context"):60110,c=e?Symbol.for("react.async_mode"):60111,l=e?Symbol.for("react.concurrent_mode"):60111,u=e?Symbol.for("react.forward_ref"):60112,p=e?Symbol.for("react.suspense"):60113,f=e?Symbol.for("react.suspense_list"):60120,d=e?Symbol.for("react.memo"):60115,m=e?Symbol.for("react.lazy"):60116,y=e?Symbol.for("react.block"):60121,g=e?Symbol.for("react.fundamental"):60117,h=e?Symbol.for("react.responder"):60118,b=e?Symbol.for("react.scope"):60119;function v(e){if("object"==typeof e&&null!==e){var f=e.$$typeof;switch(f){case r:switch(e=e.type){case c:case l:case n:case i:case o:case p:return e;default:switch(e=e&&e.$$typeof){case s:case u:case m:case d:case a:return e;default:return f}}case t:return f}}}function S(e){return v(e)===l}return D.AsyncMode=c,D.ConcurrentMode=l,D.ContextConsumer=s,D.ContextProvider=a,D.Element=r,D.ForwardRef=u,D.Fragment=n,D.Lazy=m,D.Memo=d,D.Portal=t,D.Profiler=i,D.StrictMode=o,D.Suspense=p,D.isAsyncMode=function(e){return S(e)||v(e)===c},D.isConcurrentMode=S,D.isContextConsumer=function(e){return v(e)===s},D.isContextProvider=function(e){return v(e)===a},D.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},D.isForwardRef=function(e){return v(e)===u},D.isFragment=function(e){return v(e)===n},D.isLazy=function(e){return v(e)===m},D.isMemo=function(e){return v(e)===d},D.isPortal=function(e){return v(e)===t},D.isProfiler=function(e){return v(e)===i},D.isStrictMode=function(e){return v(e)===o},D.isSuspense=function(e){return v(e)===p},D.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===n||e===l||e===i||e===o||e===p||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===d||e.$$typeof===a||e.$$typeof===s||e.$$typeof===u||e.$$typeof===g||e.$$typeof===h||e.$$typeof===b||e.$$typeof===y)},D.typeOf=v,D}():B.exports=(V||(V=1,"production"!==process.env.NODE_ENV&&function(){var e="function"==typeof Symbol&&Symbol.for,r=e?Symbol.for("react.element"):60103,t=e?Symbol.for("react.portal"):60106,n=e?Symbol.for("react.fragment"):60107,o=e?Symbol.for("react.strict_mode"):60108,i=e?Symbol.for("react.profiler"):60114,a=e?Symbol.for("react.provider"):60109,s=e?Symbol.for("react.context"):60110,c=e?Symbol.for("react.async_mode"):60111,l=e?Symbol.for("react.concurrent_mode"):60111,u=e?Symbol.for("react.forward_ref"):60112,p=e?Symbol.for("react.suspense"):60113,f=e?Symbol.for("react.suspense_list"):60120,d=e?Symbol.for("react.memo"):60115,m=e?Symbol.for("react.lazy"):60116,y=e?Symbol.for("react.block"):60121,g=e?Symbol.for("react.fundamental"):60117,h=e?Symbol.for("react.responder"):60118,b=e?Symbol.for("react.scope"):60119;function v(e){if("object"==typeof e&&null!==e){var f=e.$$typeof;switch(f){case r:var y=e.type;switch(y){case c:case l:case n:case i:case o:case p:return y;default:var g=y&&y.$$typeof;switch(g){case s:case u:case m:case d:case a:return g;default:return f}}case t:return f}}}var S=c,w=l,$=s,x=a,k=r,C=u,O=n,E=m,T=d,A=t,_=i,N=o,j=p,I=!1;function P(e){return v(e)===l}ee.AsyncMode=S,ee.ConcurrentMode=w,ee.ContextConsumer=$,ee.ContextProvider=x,ee.Element=k,ee.ForwardRef=C,ee.Fragment=O,ee.Lazy=E,ee.Memo=T,ee.Portal=A,ee.Profiler=_,ee.StrictMode=N,ee.Suspense=j,ee.isAsyncMode=function(e){return I||(I=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),P(e)||v(e)===c},ee.isConcurrentMode=P,ee.isContextConsumer=function(e){return v(e)===s},ee.isContextProvider=function(e){return v(e)===a},ee.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},ee.isForwardRef=function(e){return v(e)===u},ee.isFragment=function(e){return v(e)===n},ee.isLazy=function(e){return v(e)===m},ee.isMemo=function(e){return v(e)===d},ee.isPortal=function(e){return v(e)===t},ee.isProfiler=function(e){return v(e)===i},ee.isStrictMode=function(e){return v(e)===o},ee.isSuspense=function(e){return v(e)===p},ee.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===n||e===l||e===i||e===o||e===p||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===d||e.$$typeof===a||e.$$typeof===s||e.$$typeof===u||e.$$typeof===g||e.$$typeof===h||e.$$typeof===b||e.$$typeof===y)},ee.typeOf=v}()),ee)),B.exports}
/*
  object-assign
  (c) Sindre Sorhus
  @license MIT
  */function te(){if(U)return W;U=1;var e=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable;return W=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var r={},t=0;t<10;t++)r["_"+String.fromCharCode(t)]=t;if("**********"!==Object.getOwnPropertyNames(r).map((function(e){return r[e]})).join(""))return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach((function(e){n[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")}catch(o){return!1}}()?Object.assign:function(n,o){for(var i,a,s=function(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(n),c=1;c<arguments.length;c++){for(var l in i=Object(arguments[c]))r.call(i,l)&&(s[l]=i[l]);if(e){a=e(i);for(var u=0;u<a.length;u++)t.call(i,a[u])&&(s[a[u]]=i[a[u]])}}return s},W}function ne(){if(z)return L;z=1;return L="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}function oe(){return K?G:(K=1,G=Function.call.bind(Object.prototype.hasOwnProperty))}function ie(){if(Y)return H;Y=1;var e=function(){};if("production"!==process.env.NODE_ENV){var r=ne(),t={},n=oe();e=function(e){var r="Warning: "+e;"undefined"!=typeof console&&console.error(r);try{throw new Error(r)}catch(t){}}}function o(o,i,a,s,c){if("production"!==process.env.NODE_ENV)for(var l in o)if(n(o,l)){var u;try{if("function"!=typeof o[l]){var p=Error((s||"React class")+": "+a+" type `"+l+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof o[l]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw p.name="Invariant Violation",p}u=o[l](i,l,s,a,null,r)}catch(d){u=d}if(!u||u instanceof Error||e((s||"React class")+": type specification of "+a+" `"+l+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof u+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),u instanceof Error&&!(u.message in t)){t[u.message]=!0;var f=c?c():"";e("Failed "+a+" type: "+u.message+(null!=f?f:""))}}}return o.resetWarningCache=function(){"production"!==process.env.NODE_ENV&&(t={})},H=o}function ae(){if(J)return q;J=1;var e=re(),r=te(),t=ne(),n=oe(),o=ie(),i=function(){};function a(){return null}return"production"!==process.env.NODE_ENV&&(i=function(e){var r="Warning: "+e;"undefined"!=typeof console&&console.error(r);try{throw new Error(r)}catch(t){}}),q=function(s,c){var l="function"==typeof Symbol&&Symbol.iterator;var u="<<anonymous>>",p={array:y("array"),bigint:y("bigint"),bool:y("boolean"),func:y("function"),number:y("number"),object:y("object"),string:y("string"),symbol:y("symbol"),any:m(a),arrayOf:function(e){return m((function(r,n,o,i,a){if("function"!=typeof e)return new d("Property `"+a+"` of component `"+o+"` has invalid PropType notation inside arrayOf.");var s=r[n];if(!Array.isArray(s))return new d("Invalid "+i+" `"+a+"` of type `"+b(s)+"` supplied to `"+o+"`, expected an array.");for(var c=0;c<s.length;c++){var l=e(s,c,o,i,a+"["+c+"]",t);if(l instanceof Error)return l}return null}))},element:m((function(e,r,t,n,o){var i=e[r];return s(i)?null:new d("Invalid "+n+" `"+o+"` of type `"+b(i)+"` supplied to `"+t+"`, expected a single ReactElement.")})),elementType:m((function(r,t,n,o,i){var a=r[t];return e.isValidElementType(a)?null:new d("Invalid "+o+" `"+i+"` of type `"+b(a)+"` supplied to `"+n+"`, expected a single ReactElement type.")})),instanceOf:function(e){return m((function(r,t,n,o,i){if(!(r[t]instanceof e)){var a=e.name||u;return new d("Invalid "+o+" `"+i+"` of type `"+(((s=r[t]).constructor&&s.constructor.name?s.constructor.name:u)+"` supplied to `")+n+"`, expected instance of `"+a+"`.")}var s;return null}))},node:m((function(e,r,t,n,o){return h(e[r])?null:new d("Invalid "+n+" `"+o+"` supplied to `"+t+"`, expected a ReactNode.")})),objectOf:function(e){return m((function(r,o,i,a,s){if("function"!=typeof e)return new d("Property `"+s+"` of component `"+i+"` has invalid PropType notation inside objectOf.");var c=r[o],l=b(c);if("object"!==l)return new d("Invalid "+a+" `"+s+"` of type `"+l+"` supplied to `"+i+"`, expected an object.");for(var u in c)if(n(c,u)){var p=e(c,u,i,a,s+"."+u,t);if(p instanceof Error)return p}return null}))},oneOf:function(e){if(!Array.isArray(e))return"production"!==process.env.NODE_ENV&&i(arguments.length>1?"Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).":"Invalid argument supplied to oneOf, expected an array."),a;return m((function(r,t,n,o,i){for(var a=r[t],s=0;s<e.length;s++)if(f(a,e[s]))return null;var c=JSON.stringify(e,(function(e,r){return"symbol"===v(r)?String(r):r}));return new d("Invalid "+o+" `"+i+"` of value `"+String(a)+"` supplied to `"+n+"`, expected one of "+c+".")}))},oneOfType:function(e){if(!Array.isArray(e))return"production"!==process.env.NODE_ENV&&i("Invalid argument supplied to oneOfType, expected an instance of array."),a;for(var r=0;r<e.length;r++){var o=e[r];if("function"!=typeof o)return i("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+S(o)+" at index "+r+"."),a}return m((function(r,o,i,a,s){for(var c=[],l=0;l<e.length;l++){var u=(0,e[l])(r,o,i,a,s,t);if(null==u)return null;u.data&&n(u.data,"expectedType")&&c.push(u.data.expectedType)}return new d("Invalid "+a+" `"+s+"` supplied to `"+i+"`"+(c.length>0?", expected one of type ["+c.join(", ")+"]":"")+".")}))},shape:function(e){return m((function(r,n,o,i,a){var s=r[n],c=b(s);if("object"!==c)return new d("Invalid "+i+" `"+a+"` of type `"+c+"` supplied to `"+o+"`, expected `object`.");for(var l in e){var u=e[l];if("function"!=typeof u)return g(o,i,a,l,v(u));var p=u(s,l,o,i,a+"."+l,t);if(p)return p}return null}))},exact:function(e){return m((function(o,i,a,s,c){var l=o[i],u=b(l);if("object"!==u)return new d("Invalid "+s+" `"+c+"` of type `"+u+"` supplied to `"+a+"`, expected `object`.");var p=r({},o[i],e);for(var f in p){var m=e[f];if(n(e,f)&&"function"!=typeof m)return g(a,s,c,f,v(m));if(!m)return new d("Invalid "+s+" `"+c+"` key `"+f+"` supplied to `"+a+"`.\nBad object: "+JSON.stringify(o[i],null,"  ")+"\nValid keys: "+JSON.stringify(Object.keys(e),null,"  "));var y=m(l,f,a,s,c+"."+f,t);if(y)return y}return null}))}};function f(e,r){return e===r?0!==e||1/e==1/r:e!=e&&r!=r}function d(e,r){this.message=e,this.data=r&&"object"==typeof r?r:{},this.stack=""}function m(e){if("production"!==process.env.NODE_ENV)var r={},n=0;function o(o,a,s,l,p,f,m){if(l=l||u,f=f||s,m!==t){if(c){var y=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");throw y.name="Invariant Violation",y}if("production"!==process.env.NODE_ENV&&"undefined"!=typeof console){var g=l+":"+s;!r[g]&&n<3&&(i("You are manually calling a React.PropTypes validation function for the `"+f+"` prop on `"+l+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),r[g]=!0,n++)}}return null==a[s]?o?null===a[s]?new d("The "+p+" `"+f+"` is marked as required in `"+l+"`, but its value is `null`."):new d("The "+p+" `"+f+"` is marked as required in `"+l+"`, but its value is `undefined`."):null:e(a,s,l,p,f)}var a=o.bind(null,!1);return a.isRequired=o.bind(null,!0),a}function y(e){return m((function(r,t,n,o,i,a){var s=r[t];return b(s)!==e?new d("Invalid "+o+" `"+i+"` of type `"+v(s)+"` supplied to `"+n+"`, expected `"+e+"`.",{expectedType:e}):null}))}function g(e,r,t,n,o){return new d((e||"React class")+": "+r+" type `"+t+"."+n+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+o+"`.")}function h(e){switch(typeof e){case"number":case"string":case"undefined":return!0;case"boolean":return!e;case"object":if(Array.isArray(e))return e.every(h);if(null===e||s(e))return!0;var r=function(e){var r=e&&(l&&e[l]||e["@@iterator"]);if("function"==typeof r)return r}(e);if(!r)return!1;var t,n=r.call(e);if(r!==e.entries){for(;!(t=n.next()).done;)if(!h(t.value))return!1}else for(;!(t=n.next()).done;){var o=t.value;if(o&&!h(o[1]))return!1}return!0;default:return!1}}function b(e){var r=typeof e;return Array.isArray(e)?"array":e instanceof RegExp?"object":function(e,r){return"symbol"===e||!!r&&("Symbol"===r["@@toStringTag"]||"function"==typeof Symbol&&r instanceof Symbol)}(r,e)?"symbol":r}function v(e){if(null==e)return""+e;var r=b(e);if("object"===r){if(e instanceof Date)return"date";if(e instanceof RegExp)return"regexp"}return r}function S(e){var r=v(e);switch(r){case"array":case"object":return"an "+r;case"boolean":case"date":case"regexp":return"a "+r;default:return r}}return d.prototype=Error.prototype,p.checkPropTypes=o,p.resetWarningCache=o.resetWarningCache,p.PropTypes=p,p},q}function se(){if(Q)return X;Q=1;var e=ne();function r(){}function t(){}return t.resetWarningCache=r,X=function(){function n(r,t,n,o,i,a){if(a!==e){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function o(){return n}n.isRequired=n;var i={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:o,element:n,elementType:n,instanceOf:o,node:n,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:t,resetWarningCache:r};return i.PropTypes=i,i}}function ce(){if(Z)return M.exports;if(Z=1,"production"!==process.env.NODE_ENV){var e=re();M.exports=ae()(e.isElement,true)}else M.exports=se()();return M.exports}const le=a(ce());
/**
   * @mui/styled-engine v6.4.11
   *
   * @license MIT
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */const ue=[];function pe(e){return ue[0]=e,function(e,r,t){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var n=!0,o="";I=void 0;var i=e[0];null==i||void 0===i.raw?(n=!1,o+=j(t,r,i)):o+=i[0];for(var a=1;a<e.length;a++)o+=j(t,r,e[a]),n&&(o+=i[a]);P.lastIndex=0;for(var s,c="";null!==(s=P.exec(o));)c+="-"+s[1];var l=function(e){for(var r,t=0,n=0,o=e.length;o>=4;++n,o-=4)r=***********(65535&(r=255&e.charCodeAt(n)|(255&e.charCodeAt(++n))<<8|(255&e.charCodeAt(++n))<<16|(255&e.charCodeAt(++n))<<24))+(59797*(r>>>16)<<16),t=***********(65535&(r^=r>>>24))+(59797*(r>>>16)<<16)^***********(65535&t)+(59797*(t>>>16)<<16);switch(o){case 3:t^=(255&e.charCodeAt(n+2))<<16;case 2:t^=(255&e.charCodeAt(n+1))<<8;case 1:t=***********(65535&(t^=255&e.charCodeAt(n)))+(59797*(t>>>16)<<16)}return(((t=***********(65535&(t^=t>>>13))+(59797*(t>>>16)<<16))^t>>>15)>>>0).toString(36)}(o)+c;return{name:l,styles:o,next:I}}(ue)}var fe,de={exports:{}},me={};function ye(){if(fe)return me;fe=1;var e=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),t=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),a=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),l=Symbol.for("react.suspense_list"),u=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),f=Symbol.for("react.view_transition"),d=Symbol.for("react.client.reference");function m(d){if("object"==typeof d&&null!==d){var m=d.$$typeof;switch(m){case e:switch(d=d.type){case t:case o:case n:case c:case l:case f:return d;default:switch(d=d&&d.$$typeof){case a:case s:case p:case u:case i:return d;default:return m}}case r:return m}}}return me.ContextConsumer=i,me.ContextProvider=a,me.Element=e,me.ForwardRef=s,me.Fragment=t,me.Lazy=p,me.Memo=u,me.Portal=r,me.Profiler=o,me.StrictMode=n,me.Suspense=c,me.SuspenseList=l,me.isContextConsumer=function(e){return m(e)===i},me.isContextProvider=function(e){return m(e)===a},me.isElement=function(r){return"object"==typeof r&&null!==r&&r.$$typeof===e},me.isForwardRef=function(e){return m(e)===s},me.isFragment=function(e){return m(e)===t},me.isLazy=function(e){return m(e)===p},me.isMemo=function(e){return m(e)===u},me.isPortal=function(e){return m(e)===r},me.isProfiler=function(e){return m(e)===o},me.isStrictMode=function(e){return m(e)===n},me.isSuspense=function(e){return m(e)===c},me.isSuspenseList=function(e){return m(e)===l},me.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===t||e===o||e===n||e===c||e===l||"object"==typeof e&&null!==e&&(e.$$typeof===p||e.$$typeof===u||e.$$typeof===a||e.$$typeof===i||e.$$typeof===s||e.$$typeof===d||void 0!==e.getModuleId)},me.typeOf=m,me}var ge,he,be={};
/**
   * @license React
   * react-is.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */function ve(){return ge||(ge=1,"production"!==process.env.NODE_ENV&&function(){function e(e){if("object"==typeof e&&null!==e){var m=e.$$typeof;switch(m){case r:switch(e=e.type){case n:case i:case o:case l:case u:case d:return e;default:switch(e=e&&e.$$typeof){case s:case c:case f:case p:case a:return e;default:return m}}case t:return m}}}var r=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.consumer"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),u=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),d=Symbol.for("react.view_transition"),m=Symbol.for("react.client.reference");be.ContextConsumer=a,be.ContextProvider=s,be.Element=r,be.ForwardRef=c,be.Fragment=n,be.Lazy=f,be.Memo=p,be.Portal=t,be.Profiler=i,be.StrictMode=o,be.Suspense=l,be.SuspenseList=u,be.isContextConsumer=function(r){return e(r)===a},be.isContextProvider=function(r){return e(r)===s},be.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},be.isForwardRef=function(r){return e(r)===c},be.isFragment=function(r){return e(r)===n},be.isLazy=function(r){return e(r)===f},be.isMemo=function(r){return e(r)===p},be.isPortal=function(r){return e(r)===t},be.isProfiler=function(r){return e(r)===i},be.isStrictMode=function(r){return e(r)===o},be.isSuspense=function(r){return e(r)===l},be.isSuspenseList=function(r){return e(r)===u},be.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===n||e===i||e===o||e===l||e===u||"object"==typeof e&&null!==e&&(e.$$typeof===f||e.$$typeof===p||e.$$typeof===s||e.$$typeof===a||e.$$typeof===c||e.$$typeof===m||void 0!==e.getModuleId)},be.typeOf=e}()),be}function Se(){return he||(he=1,"production"===process.env.NODE_ENV?de.exports=ye():de.exports=ve()),de.exports}var we=Se();function $e(e){if("object"!=typeof e||null===e)return!1;const r=Object.getPrototypeOf(e);return!(null!==r&&r!==Object.prototype&&null!==Object.getPrototypeOf(r)||Symbol.toStringTag in e||Symbol.iterator in e)}function xe(e){if(i.isValidElement(e)||we.isValidElementType(e)||!$e(e))return e;const r={};return Object.keys(e).forEach((t=>{r[t]=xe(e[t])})),r}function ke(e,r,t={clone:!0}){const n=t.clone?{...e}:e;return $e(e)&&$e(r)&&Object.keys(r).forEach((o=>{i.isValidElement(r[o])||we.isValidElementType(r[o])?n[o]=r[o]:$e(r[o])&&Object.prototype.hasOwnProperty.call(e,o)&&$e(e[o])?n[o]=ke(e[o],r[o],t):t.clone?n[o]=$e(r[o])?xe(r[o]):r[o]:n[o]=r[o]})),n}function Ce(e){const{values:r={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:t="px",step:n=5,...o}=e,i=(e=>{const r=Object.keys(e).map((r=>({key:r,val:e[r]})))||[];return r.sort(((e,r)=>e.val-r.val)),r.reduce(((e,r)=>({...e,[r.key]:r.val})),{})})(r),a=Object.keys(i);function s(e){return`@media (min-width:${"number"==typeof r[e]?r[e]:e}${t})`}function c(e){return`@media (max-width:${("number"==typeof r[e]?r[e]:e)-n/100}${t})`}function l(e,o){const i=a.indexOf(o);return`@media (min-width:${"number"==typeof r[e]?r[e]:e}${t}) and (max-width:${(-1!==i&&"number"==typeof r[a[i]]?r[a[i]]:o)-n/100}${t})`}return{keys:a,values:i,up:s,down:c,between:l,only:function(e){return a.indexOf(e)+1<a.length?l(e,a[a.indexOf(e)+1]):s(e)},not:function(e){const r=a.indexOf(e);return 0===r?s(a[1]):r===a.length-1?c(a[r]):l(e,a[a.indexOf(e)+1]).replace("@media","@media not all and")},unit:t,...o}}const Oe={borderRadius:4},Ee="production"!==process.env.NODE_ENV?le.oneOfType([le.number,le.string,le.object,le.array]):{};function Te(e,r){return r?ke(e,r,{clone:!1}):e}const Ae={xs:0,sm:600,md:900,lg:1200,xl:1536},_e={keys:["xs","sm","md","lg","xl"],up:e=>`@media (min-width:${Ae[e]}px)`},Ne={containerQueries:e=>({up:r=>{let t="number"==typeof r?r:Ae[r]||r;return"number"==typeof t&&(t=`${t}px`),e?`@container ${e} (min-width:${t})`:`@container (min-width:${t})`}})};function je(e,r,t){const n=e.theme||{};if(Array.isArray(r)){const e=n.breakpoints||_e;return r.reduce(((n,o,i)=>(n[e.up(e.keys[i])]=t(r[i]),n)),{})}if("object"==typeof r){const e=n.breakpoints||_e;return Object.keys(r).reduce(((o,i)=>{if(a=e.keys,"@"===(s=i)||s.startsWith("@")&&(a.some((e=>s.startsWith(`@${e}`)))||s.match(/^@\d/))){const e=function(e,r){const t=r.match(/^@([^/]+)?\/?(.+)?$/);if(!t){if("production"!==process.env.NODE_ENV)throw new Error("production"!==process.env.NODE_ENV?`MUI: The provided shorthand (${r}) is invalid. The format should be \`@<breakpoint | number>\` or \`@<breakpoint | number>/<container>\`.\nFor example, \`@sm\` or \`@600\` or \`@40rem/sidebar\`.`:x(18,`(${r})`));return null}const[,n,o]=t,i=Number.isNaN(+n)?n||0:+n;return e.containerQueries(o).up(i)}(n.containerQueries?n:Ne,i);e&&(o[e]=t(r[i],i))}else if(Object.keys(e.values||Ae).includes(i)){o[e.up(i)]=t(r[i],i)}else{const e=i;o[e]=r[e]}var a,s;return o}),{})}return t(r)}function Ie(e){if("string"!=typeof e)throw new Error("production"!==process.env.NODE_ENV?"MUI: `capitalize(string)` expects a string argument.":x(7));return e.charAt(0).toUpperCase()+e.slice(1)}function Pe(e,r,t=!0){if(!r||"string"!=typeof r)return null;if(e&&e.vars&&t){const t=`vars.${r}`.split(".").reduce(((e,r)=>e&&e[r]?e[r]:null),e);if(null!=t)return t}return r.split(".").reduce(((e,r)=>e&&null!=e[r]?e[r]:null),e)}function Re(e,r,t,n=t){let o;return o="function"==typeof e?e(t):Array.isArray(e)?e[t]||n:Pe(e,t)||n,r&&(o=r(o,n,e)),o}function Me(e){const{prop:r,cssProperty:t=e.prop,themeKey:n,transform:o}=e,i=e=>{if(null==e[r])return null;const i=e[r],a=Pe(e.theme,n)||{};return je(e,i,(e=>{let n=Re(a,o,e);return e===n&&"string"==typeof e&&(n=Re(a,o,`${r}${"default"===e?"":Ie(e)}`,e)),!1===t?n:{[t]:n}}))};return i.propTypes="production"!==process.env.NODE_ENV?{[r]:Ee}:{},i.filterProps=[r],i}const Be={m:"margin",p:"padding"},De={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},Ve={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Fe=function(e){const r={};return t=>(void 0===r[t]&&(r[t]=e(t)),r[t])}((e=>{if(e.length>2){if(!Ve[e])return[e];e=Ve[e]}const[r,t]=e.split(""),n=Be[r],o=De[t]||"";return Array.isArray(o)?o.map((e=>n+e)):[n+o]})),We=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Ue=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"],Le=[...We,...Ue];function ze(e,r,t,n){const o=Pe(e,r,!0)??t;return"number"==typeof o||"string"==typeof o?e=>"string"==typeof e?e:("production"!==process.env.NODE_ENV&&"number"!=typeof e&&console.error(`MUI: Expected ${n} argument to be a number or a string, got ${e}.`),"string"==typeof o?`calc(${e} * ${o})`:o*e):Array.isArray(o)?e=>{if("string"==typeof e)return e;const t=Math.abs(e);"production"!==process.env.NODE_ENV&&(Number.isInteger(t)?t>o.length-1&&console.error([`MUI: The value provided (${t}) overflows.`,`The supported values are: ${JSON.stringify(o)}.`,`${t} > ${o.length-1}, you need to add the missing values.`].join("\n")):console.error([`MUI: The \`theme.${r}\` array type cannot be combined with non integer values.You should either use an integer value that can be used as index, or define the \`theme.${r}\` as a number.`].join("\n")));const n=o[t];return e>=0?n:"number"==typeof n?-n:`-${n}`}:"function"==typeof o?o:("production"!==process.env.NODE_ENV&&console.error([`MUI: The \`theme.${r}\` value (${o}) is invalid.`,"It should be a number, an array or a function."].join("\n")),()=>{})}function Ge(e){return ze(e,"spacing",8,"spacing")}function Ke(e,r){return"string"==typeof r||null==r?r:e(r)}function He(e,r,t,n){if(!r.includes(t))return null;const o=function(e,r){return t=>e.reduce(((e,n)=>(e[n]=Ke(r,t),e)),{})}(Fe(t),n);return je(e,e[t],o)}function Ye(e,r){const t=Ge(e.theme);return Object.keys(e).map((n=>He(e,r,n,t))).reduce(Te,{})}function qe(e){return Ye(e,We)}function Je(e){return Ye(e,Ue)}function Xe(e=8,r=Ge({spacing:e})){if(e.mui)return e;const t=(...e)=>{"production"!==process.env.NODE_ENV&&(e.length<=4||console.error(`MUI: Too many arguments provided, expected between 0 and 4, got ${e.length}`));return(0===e.length?[1]:e).map((e=>{const t=r(e);return"number"==typeof t?`${t}px`:t})).join(" ")};return t.mui=!0,t}function Qe(...e){const r=e.reduce(((e,r)=>(r.filterProps.forEach((t=>{e[t]=r})),e)),{}),t=e=>Object.keys(e).reduce(((t,n)=>r[n]?Te(t,r[n](e)):t),{});return t.propTypes="production"!==process.env.NODE_ENV?e.reduce(((e,r)=>Object.assign(e,r.propTypes)),{}):{},t.filterProps=e.reduce(((e,r)=>e.concat(r.filterProps)),[]),t}function Ze(e){return"number"!=typeof e?e:`${e}px solid`}function er(e,r){return Me({prop:e,themeKey:"borders",transform:r})}qe.propTypes="production"!==process.env.NODE_ENV?We.reduce(((e,r)=>(e[r]=Ee,e)),{}):{},qe.filterProps=We,Je.propTypes="production"!==process.env.NODE_ENV?Ue.reduce(((e,r)=>(e[r]=Ee,e)),{}):{},Je.filterProps=Ue,"production"===process.env.NODE_ENV||Le.reduce(((e,r)=>(e[r]=Ee,e)),{});const rr=er("border",Ze),tr=er("borderTop",Ze),nr=er("borderRight",Ze),or=er("borderBottom",Ze),ir=er("borderLeft",Ze),ar=er("borderColor"),sr=er("borderTopColor"),cr=er("borderRightColor"),lr=er("borderBottomColor"),ur=er("borderLeftColor"),pr=er("outline",Ze),fr=er("outlineColor"),dr=e=>{if(void 0!==e.borderRadius&&null!==e.borderRadius){const r=ze(e.theme,"shape.borderRadius",4,"borderRadius"),t=e=>({borderRadius:Ke(r,e)});return je(e,e.borderRadius,t)}return null};dr.propTypes="production"!==process.env.NODE_ENV?{borderRadius:Ee}:{},dr.filterProps=["borderRadius"],Qe(rr,tr,nr,or,ir,ar,sr,cr,lr,ur,dr,pr,fr);const mr=e=>{if(void 0!==e.gap&&null!==e.gap){const r=ze(e.theme,"spacing",8,"gap"),t=e=>({gap:Ke(r,e)});return je(e,e.gap,t)}return null};mr.propTypes="production"!==process.env.NODE_ENV?{gap:Ee}:{},mr.filterProps=["gap"];const yr=e=>{if(void 0!==e.columnGap&&null!==e.columnGap){const r=ze(e.theme,"spacing",8,"columnGap"),t=e=>({columnGap:Ke(r,e)});return je(e,e.columnGap,t)}return null};yr.propTypes="production"!==process.env.NODE_ENV?{columnGap:Ee}:{},yr.filterProps=["columnGap"];const gr=e=>{if(void 0!==e.rowGap&&null!==e.rowGap){const r=ze(e.theme,"spacing",8,"rowGap"),t=e=>({rowGap:Ke(r,e)});return je(e,e.rowGap,t)}return null};gr.propTypes="production"!==process.env.NODE_ENV?{rowGap:Ee}:{},gr.filterProps=["rowGap"];function hr(e,r){return"grey"===r?r:e}Qe(mr,yr,gr,Me({prop:"gridColumn"}),Me({prop:"gridRow"}),Me({prop:"gridAutoFlow"}),Me({prop:"gridAutoColumns"}),Me({prop:"gridAutoRows"}),Me({prop:"gridTemplateColumns"}),Me({prop:"gridTemplateRows"}),Me({prop:"gridTemplateAreas"}),Me({prop:"gridArea"}));function br(e){return e<=1&&0!==e?100*e+"%":e}Qe(Me({prop:"color",themeKey:"palette",transform:hr}),Me({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:hr}),Me({prop:"backgroundColor",themeKey:"palette",transform:hr}));const vr=Me({prop:"width",transform:br}),Sr=e=>{if(void 0!==e.maxWidth&&null!==e.maxWidth){const r=r=>{var t,n,o,i,a;const s=(null==(o=null==(n=null==(t=e.theme)?void 0:t.breakpoints)?void 0:n.values)?void 0:o[r])||Ae[r];return s?"px"!==(null==(a=null==(i=e.theme)?void 0:i.breakpoints)?void 0:a.unit)?{maxWidth:`${s}${e.theme.breakpoints.unit}`}:{maxWidth:s}:{maxWidth:br(r)}};return je(e,e.maxWidth,r)}return null};Sr.filterProps=["maxWidth"];const wr=Me({prop:"minWidth",transform:br}),$r=Me({prop:"height",transform:br}),xr=Me({prop:"maxHeight",transform:br}),kr=Me({prop:"minHeight",transform:br});Me({prop:"size",cssProperty:"width",transform:br}),Me({prop:"size",cssProperty:"height",transform:br});Qe(vr,Sr,wr,$r,xr,kr,Me({prop:"boxSizing"}));const Cr={border:{themeKey:"borders",transform:Ze},borderTop:{themeKey:"borders",transform:Ze},borderRight:{themeKey:"borders",transform:Ze},borderBottom:{themeKey:"borders",transform:Ze},borderLeft:{themeKey:"borders",transform:Ze},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:Ze},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:dr},color:{themeKey:"palette",transform:hr},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:hr},backgroundColor:{themeKey:"palette",transform:hr},p:{style:Je},pt:{style:Je},pr:{style:Je},pb:{style:Je},pl:{style:Je},px:{style:Je},py:{style:Je},padding:{style:Je},paddingTop:{style:Je},paddingRight:{style:Je},paddingBottom:{style:Je},paddingLeft:{style:Je},paddingX:{style:Je},paddingY:{style:Je},paddingInline:{style:Je},paddingInlineStart:{style:Je},paddingInlineEnd:{style:Je},paddingBlock:{style:Je},paddingBlockStart:{style:Je},paddingBlockEnd:{style:Je},m:{style:qe},mt:{style:qe},mr:{style:qe},mb:{style:qe},ml:{style:qe},mx:{style:qe},my:{style:qe},margin:{style:qe},marginTop:{style:qe},marginRight:{style:qe},marginBottom:{style:qe},marginLeft:{style:qe},marginX:{style:qe},marginY:{style:qe},marginInline:{style:qe},marginInlineStart:{style:qe},marginInlineEnd:{style:qe},marginBlock:{style:qe},marginBlockStart:{style:qe},marginBlockEnd:{style:qe},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:mr},rowGap:{style:gr},columnGap:{style:yr},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:br},maxWidth:{style:Sr},minWidth:{transform:br},height:{transform:br},maxHeight:{transform:br},minHeight:{transform:br},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};const Or=function(){function e(e,r,t,n){const o={[e]:r,theme:t},i=n[e];if(!i)return{[e]:r};const{cssProperty:a=e,themeKey:s,transform:c,style:l}=i;if(null==r)return null;if("typography"===s&&"inherit"===r)return{[e]:r};const u=Pe(t,s)||{};if(l)return l(o);return je(o,r,(r=>{let t=Re(u,c,r);return r===t&&"string"==typeof r&&(t=Re(u,c,`${e}${"default"===r?"":Ie(r)}`,r)),!1===a?t:{[a]:t}}))}return function r(t){const{sx:n,theme:o={}}=t||{};if(!n)return null;const i=o.unstable_sxConfig??Cr;function a(t){let n=t;if("function"==typeof t)n=t(o);else if("object"!=typeof t)return t;if(!n)return null;const a=function(e={}){var r;return(null==(r=e.keys)?void 0:r.reduce(((r,t)=>(r[e.up(t)]={},r)),{}))||{}}(o.breakpoints),s=Object.keys(a);let c=a;return Object.keys(n).forEach((t=>{const a=(s=n[t],l=o,"function"==typeof s?s(l):s);var s,l;if(null!=a)if("object"==typeof a)if(i[t])c=Te(c,e(t,a,o,i));else{const e=je({theme:o},a,(e=>({[t]:e})));!function(...e){const r=e.reduce(((e,r)=>e.concat(Object.keys(r))),[]),t=new Set(r);return e.every((e=>t.size===Object.keys(e).length))}(e,a)?c=Te(c,e):c[t]=r({sx:a,theme:o})}else c=Te(c,e(t,a,o,i))})),function(e,r){if(!e.containerQueries)return r;const t=Object.keys(r).filter((e=>e.startsWith("@container"))).sort(((e,r)=>{var t,n;const o=/min-width:\s*([0-9.]+)/;return+((null==(t=e.match(o))?void 0:t[1])||0)-+((null==(n=r.match(o))?void 0:n[1])||0)}));return t.length?t.reduce(((e,t)=>{const n=r[t];return delete e[t],e[t]=n,e}),{...r}):r}(o,(l=c,s.reduce(((e,r)=>{const t=e[r];return(!t||0===Object.keys(t).length)&&delete e[r],e}),l)));var l}return Array.isArray(n)?n.map(a):a(n)}}();function Er(e,r){var t;const n=this;if(n.vars){if(!(null==(t=n.colorSchemes)?void 0:t[e])||"function"!=typeof n.getColorSchemeSelector)return{};let o=n.getColorSchemeSelector(e);return"&"===o?r:((o.includes("data-")||o.includes("."))&&(o=`*:where(${o.replace(/\s*&$/,"")}) &`),{[o]:r})}return n.palette.mode===e?r:{}}function Tr(e={},...r){const{breakpoints:t={},palette:n={},spacing:o,shape:i={},...a}=e;let s=ke({breakpoints:Ce(t),direction:"ltr",components:{},palette:{mode:"light",...n},spacing:Xe(o),shape:{...Oe,...i}},a);return s=function(e){const r=(e,r)=>e.replace("@media",r?`@container ${r}`:"@container");function t(t,n){t.up=(...t)=>r(e.breakpoints.up(...t),n),t.down=(...t)=>r(e.breakpoints.down(...t),n),t.between=(...t)=>r(e.breakpoints.between(...t),n),t.only=(...t)=>r(e.breakpoints.only(...t),n),t.not=(...t)=>{const o=r(e.breakpoints.not(...t),n);return o.includes("not all and")?o.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):o}}const n={},o=e=>(t(n,e),n);return t(o),{...e,containerQueries:o}}(s),s.applyStyles=Er,s=r.reduce(((e,r)=>ke(e,r)),s),s.unstable_sxConfig={...Cr,...null==a?void 0:a.unstable_sxConfig},s.unstable_sx=function(e){return Or({sx:e,theme:this})},s}Or.filterProps=["sx"];const Ar=e=>e,_r=(()=>{let e=Ar;return{configure(r){e=r},generate:r=>e(r),reset(){e=Ar}}})(),Nr={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function jr(e,r,t="Mui"){const n=Nr[r];return n?`${t}-${n}`:`${_r.generate(e)}-${r}`}function Ir(e,r=""){return e.displayName||e.name||r}function Pr(e,r,t){const n=Ir(r);return e.displayName||(""!==n?`${t}(${n})`:t)}const Rr=Tr();function Mr(e){return"ownerState"!==e&&"theme"!==e&&"sx"!==e&&"as"!==e}function Br(e){return e?(r,t)=>t[e]:null}function Dr(e,r){const t="function"==typeof r?r(e):r;if(Array.isArray(t))return t.flatMap((r=>Dr(e,r)));if(Array.isArray(null==t?void 0:t.variants)){let r;if(t.isProcessed)r=t.style;else{const{variants:e,...n}=t;r=n}return Vr(e,t.variants,[r])}return(null==t?void 0:t.isProcessed)?t.style:t}function Vr(e,r,t=[]){var n;let o;e:for(let i=0;i<r.length;i+=1){const a=r[i];if("function"==typeof a.props){if(o??(o={...e,...e.ownerState,ownerState:e.ownerState}),!a.props(o))continue}else for(const r in a.props)if(e[r]!==a.props[r]&&(null==(n=e.ownerState)?void 0:n[r])!==a.props[r])continue e;"function"==typeof a.style?(o??(o={...e,...e.ownerState,ownerState:e.ownerState}),t.push(a.style(o))):t.push(a.style)}return t}function Fr(e,r){let t;return"production"!==process.env.NODE_ENV&&e&&(t=`${e}-${Wr(r||"Root")}`),t}function Wr(e){return e?e.charAt(0).toLowerCase()+e.slice(1):e}function Ur(e,r=0,t=1){return"production"!==process.env.NODE_ENV&&(e<r||e>t)&&console.error(`MUI: The value provided ${e} is out of range [${r}, ${t}].`),function(e,r=Number.MIN_SAFE_INTEGER,t=Number.MAX_SAFE_INTEGER){return Math.max(r,Math.min(e,t))}(e,r,t)}function Lr(e){if(e.type)return e;if("#"===e.charAt(0))return Lr(function(e){e=e.slice(1);const r=new RegExp(`.{1,${e.length>=6?2:1}}`,"g");let t=e.match(r);return t&&1===t[0].length&&(t=t.map((e=>e+e))),"production"!==process.env.NODE_ENV&&e.length!==e.trim().length&&console.error(`MUI: The color: "${e}" is invalid. Make sure the color input doesn't contain leading/trailing space.`),t?`rgb${4===t.length?"a":""}(${t.map(((e,r)=>r<3?parseInt(e,16):Math.round(parseInt(e,16)/255*1e3)/1e3)).join(", ")})`:""}(e));const r=e.indexOf("("),t=e.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(t))throw new Error("production"!==process.env.NODE_ENV?`MUI: Unsupported \`${e}\` color.\nThe following formats are supported: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().`:x(9,e));let n,o=e.substring(r+1,e.length-1);if("color"===t){if(o=o.split(" "),n=o.shift(),4===o.length&&"/"===o[3].charAt(0)&&(o[3]=o[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(n))throw new Error("production"!==process.env.NODE_ENV?`MUI: unsupported \`${n}\` color space.\nThe following color spaces are supported: srgb, display-p3, a98-rgb, prophoto-rgb, rec-2020.`:x(10,n))}else o=o.split(",");return o=o.map((e=>parseFloat(e))),{type:t,values:o,colorSpace:n}}const zr=(e,r)=>{try{return(e=>{const r=Lr(e);return r.values.slice(0,3).map(((e,t)=>r.type.includes("hsl")&&0!==t?`${e}%`:e)).join(" ")})(e)}catch(t){return r&&"production"!==process.env.NODE_ENV&&console.warn(r),e}};function Gr(e){const{type:r,colorSpace:t}=e;let{values:n}=e;return r.includes("rgb")?n=n.map(((e,r)=>r<3?parseInt(e,10):e)):r.includes("hsl")&&(n[1]=`${n[1]}%`,n[2]=`${n[2]}%`),n=r.includes("color")?`${t} ${n.join(" ")}`:`${n.join(", ")}`,`${r}(${n})`}function Kr(e){e=Lr(e);const{values:r}=e,t=r[0],n=r[1]/100,o=r[2]/100,i=n*Math.min(o,1-o),a=(e,r=(e+t/30)%12)=>o-i*Math.max(Math.min(r-3,9-r,1),-1);let s="rgb";const c=[Math.round(255*a(0)),Math.round(255*a(8)),Math.round(255*a(4))];return"hsla"===e.type&&(s+="a",c.push(r[3])),Gr({type:s,values:c})}function Hr(e){let r="hsl"===(e=Lr(e)).type||"hsla"===e.type?Lr(Kr(e)).values:e.values;return r=r.map((r=>("color"!==e.type&&(r/=255),r<=.03928?r/12.92:((r+.055)/1.055)**2.4))),Number((.2126*r[0]+.7152*r[1]+.0722*r[2]).toFixed(3))}function Yr(e,r){const t=Hr(e),n=Hr(r);return(Math.max(t,n)+.05)/(Math.min(t,n)+.05)}function qr(e,r,t){try{return function(e,r){return e=Lr(e),r=Ur(r),"rgb"!==e.type&&"hsl"!==e.type||(e.type+="a"),"color"===e.type?e.values[3]=`/${r}`:e.values[3]=r,Gr(e)}(e,r)}catch(n){return e}}function Jr(e,r){if(e=Lr(e),r=Ur(r),e.type.includes("hsl"))e.values[2]*=1-r;else if(e.type.includes("rgb")||e.type.includes("color"))for(let t=0;t<3;t+=1)e.values[t]*=1-r;return Gr(e)}function Xr(e,r,t){try{return Jr(e,r)}catch(n){return e}}function Qr(e,r){if(e=Lr(e),r=Ur(r),e.type.includes("hsl"))e.values[2]+=(100-e.values[2])*r;else if(e.type.includes("rgb"))for(let t=0;t<3;t+=1)e.values[t]+=(255-e.values[t])*r;else if(e.type.includes("color"))for(let t=0;t<3;t+=1)e.values[t]+=(1-e.values[t])*r;return Gr(e)}function Zr(e,r,t){try{return Qr(e,r)}catch(n){return e}}function et(e,r,t){try{return function(e,r=.15){return Hr(e)>.5?Jr(e,r):Qr(e,r)}(e,r)}catch(n){return e}}function rt(e=""){function r(...t){if(!t.length)return"";const n=t[0];return"string"!=typeof n||n.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, ${n}`:`, var(--${e?`${e}-`:""}${n}${r(...t.slice(1))})`}return(t,...n)=>`var(--${e?`${e}-`:""}${t}${r(...n)})`}const tt=(e,r,t,n=[])=>{let o=e;r.forEach(((e,i)=>{i===r.length-1?Array.isArray(o)?o[Number(e)]=t:o&&"object"==typeof o&&(o[e]=t):o&&"object"==typeof o&&(o[e]||(o[e]=n.includes(e)?[]:{}),o=o[e])}))};function nt(e,r){const{prefix:t,shouldSkipGeneratingVar:n}=r||{},o={},i={},a={};var s,c;return s=(e,r,s)=>{if(!("string"!=typeof r&&"number"!=typeof r||n&&n(e,r))){const n=`--${t?`${t}-`:""}${e.join("-")}`,c=((e,r)=>"number"==typeof r?["lineHeight","fontWeight","opacity","zIndex"].some((r=>e.includes(r)))||e[e.length-1].toLowerCase().includes("opacity")?r:`${r}px`:r)(e,r);Object.assign(o,{[n]:c}),tt(i,e,`var(${n})`,s),tt(a,e,`var(${n}, ${c})`,s)}},c=e=>"vars"===e[0],function e(r,t=[],n=[]){Object.entries(r).forEach((([r,o])=>{(!c||c&&!c([...t,r]))&&null!=o&&("object"==typeof o&&Object.keys(o).length>0?e(o,[...t,r],Array.isArray(o)?[...n,r]:n):s([...t,r],o,n))}))}(e),{css:o,vars:i,varsWithDefaults:a}}const ot={black:"#000",white:"#fff"},it={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},at="#f3e5f5",st="#ce93d8",ct="#ba68c8",lt="#ab47bc",ut="#9c27b0",pt="#7b1fa2",ft="#e57373",dt="#ef5350",mt="#f44336",yt="#d32f2f",gt="#c62828",ht="#ffb74d",bt="#ffa726",vt="#ff9800",St="#f57c00",wt="#e65100",$t="#e3f2fd",xt="#90caf9",kt="#42a5f5",Ct="#1976d2",Ot="#1565c0",Et="#4fc3f7",Tt="#29b6f6",At="#03a9f4",_t="#0288d1",Nt="#01579b",jt="#81c784",It="#66bb6a",Pt="#4caf50",Rt="#388e3c",Mt="#2e7d32",Bt="#1b5e20";function Dt(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:ot.white,default:ot.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const Vt=Dt();function Ft(){return{text:{primary:ot.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:ot.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const Wt=Ft();function Ut(e,r,t,n){const o=n.light||n,i=n.dark||1.5*n;e[r]||(e.hasOwnProperty(t)?e[r]=e[t]:"light"===r?e.light=Qr(e.main,o):"dark"===r&&(e.dark=Jr(e.main,i)))}function Lt(e){const{mode:r="light",contrastThreshold:t=3,tonalOffset:n=.2,...o}=e,i=e.primary||function(e="light"){return"dark"===e?{main:xt,light:$t,dark:kt}:{main:Ct,light:kt,dark:Ot}}(r),a=e.secondary||function(e="light"){return"dark"===e?{main:st,light:at,dark:lt}:{main:ut,light:ct,dark:pt}}(r),s=e.error||function(e="light"){return"dark"===e?{main:mt,light:ft,dark:yt}:{main:yt,light:dt,dark:gt}}(r),c=e.info||function(e="light"){return"dark"===e?{main:Tt,light:Et,dark:_t}:{main:_t,light:At,dark:Nt}}(r),l=e.success||function(e="light"){return"dark"===e?{main:It,light:jt,dark:Rt}:{main:Mt,light:Pt,dark:Bt}}(r),u=e.warning||function(e="light"){return"dark"===e?{main:bt,light:ht,dark:St}:{main:"#ed6c02",light:vt,dark:wt}}(r);function p(e){const r=Yr(e,Wt.text.primary)>=t?Wt.text.primary:Vt.text.primary;if("production"!==process.env.NODE_ENV){const t=Yr(e,r);t<3&&console.error([`MUI: The contrast ratio of ${t}:1 for ${r} on ${e}`,"falls below the WCAG recommended absolute minimum contrast ratio of 3:1.","https://www.w3.org/TR/2008/REC-WCAG20-20081211/#visual-audio-contrast-contrast"].join("\n"))}return r}const f=({color:e,name:r,mainShade:t=500,lightShade:o=300,darkShade:i=700})=>{if(!(e={...e}).main&&e[t]&&(e.main=e[t]),!e.hasOwnProperty("main"))throw new Error("production"!==process.env.NODE_ENV?`MUI: The color${r?` (${r})`:""} provided to augmentColor(color) is invalid.\nThe color object needs to have a \`main\` property or a \`${t}\` property.`:x(11,r?` (${r})`:"",t));if("string"!=typeof e.main)throw new Error("production"!==process.env.NODE_ENV?`MUI: The color${r?` (${r})`:""} provided to augmentColor(color) is invalid.\n\`color.main\` should be a string, but \`${JSON.stringify(e.main)}\` was provided instead.\n\nDid you intend to use one of the following approaches?\n\nimport { green } from "@mui/material/colors";\n\nconst theme1 = createTheme({ palette: {\n  primary: green,\n} });\n\nconst theme2 = createTheme({ palette: {\n  primary: { main: green[500] },\n} });`:x(12,r?` (${r})`:"",JSON.stringify(e.main)));return Ut(e,"light",o,n),Ut(e,"dark",i,n),e.contrastText||(e.contrastText=p(e.main)),e};let d;"light"===r?d=Dt():"dark"===r&&(d=Ft()),"production"!==process.env.NODE_ENV&&(d||console.error(`MUI: The palette mode \`${r}\` is not supported.`));return ke({common:{...ot},mode:r,primary:f({color:i,name:"primary"}),secondary:f({color:a,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:f({color:s,name:"error"}),warning:f({color:u,name:"warning"}),info:f({color:c,name:"info"}),success:f({color:l,name:"success"}),grey:it,contrastThreshold:t,getContrastText:p,augmentColor:f,tonalOffset:n,...d},o)}function zt(e){const r={};return Object.entries(e).forEach((e=>{const[t,n]=e;"object"==typeof n&&(r[t]=`${n.fontStyle?`${n.fontStyle} `:""}${n.fontVariant?`${n.fontVariant} `:""}${n.fontWeight?`${n.fontWeight} `:""}${n.fontStretch?`${n.fontStretch} `:""}${n.fontSize||""}${n.lineHeight?`/${n.lineHeight} `:""}${n.fontFamily||""}`)})),r}const Gt={textTransform:"uppercase"},Kt='"Roboto", "Helvetica", "Arial", sans-serif';function Ht(e,r){const{fontFamily:t=Kt,fontSize:n=14,fontWeightLight:o=300,fontWeightRegular:i=400,fontWeightMedium:a=500,fontWeightBold:s=700,htmlFontSize:c=16,allVariants:l,pxToRem:u,...p}="function"==typeof r?r(e):r;"production"!==process.env.NODE_ENV&&("number"!=typeof n&&console.error("MUI: `fontSize` is required to be a number."),"number"!=typeof c&&console.error("MUI: `htmlFontSize` is required to be a number."));const f=n/14,d=u||(e=>e/c*f+"rem"),m=(e,r,n,o,i)=>{return{fontFamily:t,fontWeight:e,fontSize:d(r),lineHeight:n,...t===Kt?{letterSpacing:(a=o/r,Math.round(1e5*a)/1e5)+"em"}:{},...i,...l};var a},y={h1:m(o,96,1.167,-1.5),h2:m(o,60,1.2,-.5),h3:m(i,48,1.167,0),h4:m(i,34,1.235,.25),h5:m(i,24,1.334,0),h6:m(a,20,1.6,.15),subtitle1:m(i,16,1.75,.15),subtitle2:m(a,14,1.57,.1),body1:m(i,16,1.5,.15),body2:m(i,14,1.43,.15),button:m(a,14,1.75,.4,Gt),caption:m(i,12,1.66,.4),overline:m(i,12,2.66,1,Gt),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return ke({htmlFontSize:c,pxToRem:d,fontFamily:t,fontSize:n,fontWeightLight:o,fontWeightRegular:i,fontWeightMedium:a,fontWeightBold:s,...y},p,{clone:!1})}function Yt(...e){return[`${e[0]}px ${e[1]}px ${e[2]}px ${e[3]}px rgba(0,0,0,0.2)`,`${e[4]}px ${e[5]}px ${e[6]}px ${e[7]}px rgba(0,0,0,0.14)`,`${e[8]}px ${e[9]}px ${e[10]}px ${e[11]}px rgba(0,0,0,0.12)`].join(",")}const qt=["none",Yt(0,2,1,-1,0,1,1,0,0,1,3,0),Yt(0,3,1,-2,0,2,2,0,0,1,5,0),Yt(0,3,3,-2,0,3,4,0,0,1,8,0),Yt(0,2,4,-1,0,4,5,0,0,1,10,0),Yt(0,3,5,-1,0,5,8,0,0,1,14,0),Yt(0,3,5,-1,0,6,10,0,0,1,18,0),Yt(0,4,5,-2,0,7,10,1,0,2,16,1),Yt(0,5,5,-3,0,8,10,1,0,3,14,2),Yt(0,5,6,-3,0,9,12,1,0,3,16,2),Yt(0,6,6,-3,0,10,14,1,0,4,18,3),Yt(0,6,7,-4,0,11,15,1,0,4,20,3),Yt(0,7,8,-4,0,12,17,2,0,5,22,4),Yt(0,7,8,-4,0,13,19,2,0,5,24,4),Yt(0,7,9,-4,0,14,21,2,0,5,26,4),Yt(0,8,9,-5,0,15,22,2,0,6,28,5),Yt(0,8,10,-5,0,16,24,2,0,6,30,5),Yt(0,8,11,-5,0,17,26,2,0,6,32,5),Yt(0,9,11,-5,0,18,28,2,0,7,34,6),Yt(0,9,12,-6,0,19,29,2,0,7,36,6),Yt(0,10,13,-6,0,20,31,3,0,8,38,7),Yt(0,10,13,-6,0,21,33,3,0,8,40,7),Yt(0,10,14,-6,0,22,35,3,0,8,42,7),Yt(0,11,14,-7,0,23,36,3,0,9,44,8),Yt(0,11,15,-7,0,24,38,3,0,9,46,8)],Jt={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},Xt={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Qt(e){return`${Math.round(e)}ms`}function Zt(e){if(!e)return 0;const r=e/36;return Math.min(Math.round(10*(4+15*r**.25+r/5)),3e3)}function en(e){const r={...Jt,...e.easing},t={...Xt,...e.duration};return{getAutoHeightDuration:Zt,create:(e=["all"],n={})=>{const{duration:o=t.standard,easing:i=r.easeInOut,delay:a=0,...s}=n;if("production"!==process.env.NODE_ENV){const r=e=>"string"==typeof e,t=e=>!Number.isNaN(parseFloat(e));r(e)||Array.isArray(e)||console.error('MUI: Argument "props" must be a string or Array.'),t(o)||r(o)||console.error(`MUI: Argument "duration" must be a number or a string but found ${o}.`),r(i)||console.error('MUI: Argument "easing" must be a string.'),t(a)||r(a)||console.error('MUI: Argument "delay" must be a number or a string.'),"object"!=typeof n&&console.error(["MUI: Secong argument of transition.create must be an object.","Arguments should be either `create('prop1', options)` or `create(['prop1', 'prop2'], options)`"].join("\n")),0!==Object.keys(s).length&&console.error(`MUI: Unrecognized argument(s) [${Object.keys(s).join(",")}].`)}return(Array.isArray(e)?e:[e]).map((e=>`${e} ${"string"==typeof o?o:Qt(o)} ${i} ${"string"==typeof a?a:Qt(a)}`)).join(",")},...e,easing:r,duration:t}}const rn={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function tn(e={}){const r={...e};return function e(r){const t=Object.entries(r);for(let o=0;o<t.length;o++){const[i,a]=t[o];!$e(n=a)&&void 0!==n&&"string"!=typeof n&&"boolean"!=typeof n&&"number"!=typeof n&&!Array.isArray(n)||i.startsWith("unstable_")?delete r[i]:$e(a)&&(r[i]={...a},e(r[i]))}var n}(r),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';\n\nconst theme = ${JSON.stringify(r,null,2)};\n\ntheme.breakpoints = createBreakpoints(theme.breakpoints || {});\ntheme.transitions = createTransitions(theme.transitions || {});\n\nexport default theme;`}function nn(e={},...r){const{breakpoints:t,mixins:n={},spacing:o,palette:i={},transitions:a={},typography:s={},shape:c,...l}=e;if(e.vars&&void 0===e.generateThemeVars)throw new Error("production"!==process.env.NODE_ENV?"MUI: `vars` is a private field used for CSS variables support.\nPlease use another name or follow the [docs](https://mui.com/material-ui/customization/css-theme-variables/usage/) to enable the feature.":x(20));const u=Lt(i),p=Tr(e);let f=ke(p,{mixins:(d=p.breakpoints,m=n,{toolbar:{minHeight:56,[d.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[d.up("sm")]:{minHeight:64}},...m}),palette:u,shadows:qt.slice(),typography:Ht(u,s),transitions:en(a),zIndex:{...rn}});var d,m;if(f=ke(f,l),f=r.reduce(((e,r)=>ke(e,r)),f),"production"!==process.env.NODE_ENV){const e=["active","checked","completed","disabled","error","expanded","focused","focusVisible","required","selected"],r=(r,t)=>{let n;for(n in r){const o=r[n];if(e.includes(n)&&Object.keys(o).length>0){if("production"!==process.env.NODE_ENV){const e=jr("",n);console.error([`MUI: The \`${t}\` component increases the CSS specificity of the \`${n}\` internal state.`,"You can not override it like this: ",JSON.stringify(r,null,2),"",`Instead, you need to use the '&.${e}' syntax:`,JSON.stringify({root:{[`&.${e}`]:o}},null,2),"","https://mui.com/r/state-classes-guide"].join("\n"))}r[n]={}}}};Object.keys(f.components).forEach((e=>{const t=f.components[e].styleOverrides;t&&e.startsWith("Mui")&&r(t,e)}))}return f.unstable_sxConfig={...Cr,...null==l?void 0:l.unstable_sxConfig},f.unstable_sx=function(e){return Or({sx:e,theme:this})},f.toRuntimeSource=tn,f}const on=[...Array(25)].map(((e,r)=>{if(0===r)return"none";const t=function(e){let r;return r=e<1?5.11916*e**2:4.5*Math.log(e+1)+2,Math.round(10*r)/1e3}(r);return`linear-gradient(rgba(*********** / ${t}), rgba(*********** / ${t}))`}));function an(e){return{inputPlaceholder:"dark"===e?.5:.42,inputUnderline:"dark"===e?.7:.42,switchTrackDisabled:"dark"===e?.2:.12,switchTrack:"dark"===e?.3:.38}}function sn(e){return"dark"===e?on:[]}function cn(e){var r;return!!e[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!e[0].match(/sxConfig$/)||"palette"===e[0]&&!!(null==(r=e[1])?void 0:r.match(/(mode|contrastThreshold|tonalOffset)/))}const ln=e=>(r,t)=>{const n=e.rootSelector||":root",o=e.colorSchemeSelector;let i=o;if("class"===o&&(i=".%s"),"data"===o&&(i="[data-%s]"),(null==o?void 0:o.startsWith("data-"))&&!o.includes("%s")&&(i=`[${o}="%s"]`),e.defaultColorScheme===r){if("dark"===r){const o={};return(a=e.cssVarPrefix,[...[...Array(25)].map(((e,r)=>`--${a?`${a}-`:""}overlays-${r}`)),`--${a?`${a}-`:""}palette-AppBar-darkBg`,`--${a?`${a}-`:""}palette-AppBar-darkColor`]).forEach((e=>{o[e]=t[e],delete t[e]})),"media"===i?{[n]:t,"@media (prefers-color-scheme: dark)":{[n]:o}}:i?{[i.replace("%s",r)]:o,[`${n}, ${i.replace("%s",r)}`]:t}:{[n]:{...t,...o}}}if(i&&"media"!==i)return`${n}, ${i.replace("%s",String(r))}`}else if(r){if("media"===i)return{[`@media (prefers-color-scheme: ${String(r)})`]:{[n]:t}};if(i)return i.replace("%s",String(r))}var a;return n};function un(e,r,t){!e[r]&&t&&(e[r]=t)}function pn(e){return"string"==typeof e&&e.startsWith("hsl")?Kr(e):e}function fn(e,r){`${r}Channel`in e||(e[`${r}Channel`]=zr(pn(e[r]),`MUI: Can't create \`palette.${r}Channel\` because \`palette.${r}\` is not one of these formats: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().\nTo suppress this warning, you need to explicitly provide the \`palette.${r}Channel\` as a string (in rgb format, for example "12 12 12") or undefined if you want to remove the channel token.`))}const dn=e=>{try{return e()}catch(r){}};function mn(e,r,t,n){if(!r)return;r=!0===r?{}:r;const o="dark"===n?"dark":"light";if(!t)return void(e[n]=function(e){const{palette:r={mode:"light"},opacity:t,overlays:n,...o}=e,i=Lt(r);return{palette:i,opacity:{...an(i.mode),...t},overlays:n||sn(i.mode),...o}}({...r,palette:{mode:o,...null==r?void 0:r.palette}}));const{palette:i,...a}=nn({...t,palette:{mode:o,...null==r?void 0:r.palette}});return e[n]={...r,palette:i,opacity:{...an(o),...null==r?void 0:r.opacity},overlays:(null==r?void 0:r.overlays)||sn(o)},a}function yn(e={},...r){const{colorSchemes:t={light:!0},defaultColorScheme:n,disableCssColorScheme:o=!1,cssVarPrefix:i="mui",shouldSkipGeneratingVar:a=cn,colorSchemeSelector:s=(t.light&&t.dark?"media":void 0),rootSelector:c=":root",...l}=e,u=Object.keys(t)[0],p=n||(t.light&&"light"!==u?"light":u),f=((e="mui")=>rt(e))(i),{[p]:d,light:m,dark:y,...g}=t,h={...g};let b=d;if(("dark"===p&&!("dark"in t)||"light"===p&&!("light"in t))&&(b=!0),!b)throw new Error("production"!==process.env.NODE_ENV?`MUI: The \`colorSchemes.${p}\` option is either missing or invalid.`:x(21,p));const v=mn(h,b,l,p);m&&!h.light&&mn(h,m,void 0,"light"),y&&!h.dark&&mn(h,y,void 0,"dark");let S={defaultColorScheme:p,...v,cssVarPrefix:i,colorSchemeSelector:s,rootSelector:c,getCssVar:f,colorSchemes:h,font:{...zt(v.typography),...v.font},spacing:(w=l.spacing,"number"==typeof w?`${w}px`:"string"==typeof w||"function"==typeof w||Array.isArray(w)?w:"8px")};var w;Object.keys(S.colorSchemes).forEach((e=>{const r=S.colorSchemes[e].palette,t=e=>{const t=e.split("-"),n=t[1],o=t[2];return f(e,r[n][o])};var n;if("light"===r.mode&&(un(r.common,"background","#fff"),un(r.common,"onBackground","#000")),"dark"===r.mode&&(un(r.common,"background","#000"),un(r.common,"onBackground","#fff")),n=r,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"].forEach((e=>{n[e]||(n[e]={})})),"light"===r.mode){un(r.Alert,"errorColor",Xr(r.error.light,.6)),un(r.Alert,"infoColor",Xr(r.info.light,.6)),un(r.Alert,"successColor",Xr(r.success.light,.6)),un(r.Alert,"warningColor",Xr(r.warning.light,.6)),un(r.Alert,"errorFilledBg",t("palette-error-main")),un(r.Alert,"infoFilledBg",t("palette-info-main")),un(r.Alert,"successFilledBg",t("palette-success-main")),un(r.Alert,"warningFilledBg",t("palette-warning-main")),un(r.Alert,"errorFilledColor",dn((()=>r.getContrastText(r.error.main)))),un(r.Alert,"infoFilledColor",dn((()=>r.getContrastText(r.info.main)))),un(r.Alert,"successFilledColor",dn((()=>r.getContrastText(r.success.main)))),un(r.Alert,"warningFilledColor",dn((()=>r.getContrastText(r.warning.main)))),un(r.Alert,"errorStandardBg",Zr(r.error.light,.9)),un(r.Alert,"infoStandardBg",Zr(r.info.light,.9)),un(r.Alert,"successStandardBg",Zr(r.success.light,.9)),un(r.Alert,"warningStandardBg",Zr(r.warning.light,.9)),un(r.Alert,"errorIconColor",t("palette-error-main")),un(r.Alert,"infoIconColor",t("palette-info-main")),un(r.Alert,"successIconColor",t("palette-success-main")),un(r.Alert,"warningIconColor",t("palette-warning-main")),un(r.AppBar,"defaultBg",t("palette-grey-100")),un(r.Avatar,"defaultBg",t("palette-grey-400")),un(r.Button,"inheritContainedBg",t("palette-grey-300")),un(r.Button,"inheritContainedHoverBg",t("palette-grey-A100")),un(r.Chip,"defaultBorder",t("palette-grey-400")),un(r.Chip,"defaultAvatarColor",t("palette-grey-700")),un(r.Chip,"defaultIconColor",t("palette-grey-700")),un(r.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),un(r.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),un(r.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),un(r.LinearProgress,"primaryBg",Zr(r.primary.main,.62)),un(r.LinearProgress,"secondaryBg",Zr(r.secondary.main,.62)),un(r.LinearProgress,"errorBg",Zr(r.error.main,.62)),un(r.LinearProgress,"infoBg",Zr(r.info.main,.62)),un(r.LinearProgress,"successBg",Zr(r.success.main,.62)),un(r.LinearProgress,"warningBg",Zr(r.warning.main,.62)),un(r.Skeleton,"bg",`rgba(${t("palette-text-primaryChannel")} / 0.11)`),un(r.Slider,"primaryTrack",Zr(r.primary.main,.62)),un(r.Slider,"secondaryTrack",Zr(r.secondary.main,.62)),un(r.Slider,"errorTrack",Zr(r.error.main,.62)),un(r.Slider,"infoTrack",Zr(r.info.main,.62)),un(r.Slider,"successTrack",Zr(r.success.main,.62)),un(r.Slider,"warningTrack",Zr(r.warning.main,.62));const e=et(r.background.default,.8);un(r.SnackbarContent,"bg",e),un(r.SnackbarContent,"color",dn((()=>r.getContrastText(e)))),un(r.SpeedDialAction,"fabHoverBg",et(r.background.paper,.15)),un(r.StepConnector,"border",t("palette-grey-400")),un(r.StepContent,"border",t("palette-grey-400")),un(r.Switch,"defaultColor",t("palette-common-white")),un(r.Switch,"defaultDisabledColor",t("palette-grey-100")),un(r.Switch,"primaryDisabledColor",Zr(r.primary.main,.62)),un(r.Switch,"secondaryDisabledColor",Zr(r.secondary.main,.62)),un(r.Switch,"errorDisabledColor",Zr(r.error.main,.62)),un(r.Switch,"infoDisabledColor",Zr(r.info.main,.62)),un(r.Switch,"successDisabledColor",Zr(r.success.main,.62)),un(r.Switch,"warningDisabledColor",Zr(r.warning.main,.62)),un(r.TableCell,"border",Zr(qr(r.divider,1),.88)),un(r.Tooltip,"bg",qr(r.grey[700],.92))}if("dark"===r.mode){un(r.Alert,"errorColor",Zr(r.error.light,.6)),un(r.Alert,"infoColor",Zr(r.info.light,.6)),un(r.Alert,"successColor",Zr(r.success.light,.6)),un(r.Alert,"warningColor",Zr(r.warning.light,.6)),un(r.Alert,"errorFilledBg",t("palette-error-dark")),un(r.Alert,"infoFilledBg",t("palette-info-dark")),un(r.Alert,"successFilledBg",t("palette-success-dark")),un(r.Alert,"warningFilledBg",t("palette-warning-dark")),un(r.Alert,"errorFilledColor",dn((()=>r.getContrastText(r.error.dark)))),un(r.Alert,"infoFilledColor",dn((()=>r.getContrastText(r.info.dark)))),un(r.Alert,"successFilledColor",dn((()=>r.getContrastText(r.success.dark)))),un(r.Alert,"warningFilledColor",dn((()=>r.getContrastText(r.warning.dark)))),un(r.Alert,"errorStandardBg",Xr(r.error.light,.9)),un(r.Alert,"infoStandardBg",Xr(r.info.light,.9)),un(r.Alert,"successStandardBg",Xr(r.success.light,.9)),un(r.Alert,"warningStandardBg",Xr(r.warning.light,.9)),un(r.Alert,"errorIconColor",t("palette-error-main")),un(r.Alert,"infoIconColor",t("palette-info-main")),un(r.Alert,"successIconColor",t("palette-success-main")),un(r.Alert,"warningIconColor",t("palette-warning-main")),un(r.AppBar,"defaultBg",t("palette-grey-900")),un(r.AppBar,"darkBg",t("palette-background-paper")),un(r.AppBar,"darkColor",t("palette-text-primary")),un(r.Avatar,"defaultBg",t("palette-grey-600")),un(r.Button,"inheritContainedBg",t("palette-grey-800")),un(r.Button,"inheritContainedHoverBg",t("palette-grey-700")),un(r.Chip,"defaultBorder",t("palette-grey-700")),un(r.Chip,"defaultAvatarColor",t("palette-grey-300")),un(r.Chip,"defaultIconColor",t("palette-grey-300")),un(r.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),un(r.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),un(r.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),un(r.LinearProgress,"primaryBg",Xr(r.primary.main,.5)),un(r.LinearProgress,"secondaryBg",Xr(r.secondary.main,.5)),un(r.LinearProgress,"errorBg",Xr(r.error.main,.5)),un(r.LinearProgress,"infoBg",Xr(r.info.main,.5)),un(r.LinearProgress,"successBg",Xr(r.success.main,.5)),un(r.LinearProgress,"warningBg",Xr(r.warning.main,.5)),un(r.Skeleton,"bg",`rgba(${t("palette-text-primaryChannel")} / 0.13)`),un(r.Slider,"primaryTrack",Xr(r.primary.main,.5)),un(r.Slider,"secondaryTrack",Xr(r.secondary.main,.5)),un(r.Slider,"errorTrack",Xr(r.error.main,.5)),un(r.Slider,"infoTrack",Xr(r.info.main,.5)),un(r.Slider,"successTrack",Xr(r.success.main,.5)),un(r.Slider,"warningTrack",Xr(r.warning.main,.5));const e=et(r.background.default,.98);un(r.SnackbarContent,"bg",e),un(r.SnackbarContent,"color",dn((()=>r.getContrastText(e)))),un(r.SpeedDialAction,"fabHoverBg",et(r.background.paper,.15)),un(r.StepConnector,"border",t("palette-grey-600")),un(r.StepContent,"border",t("palette-grey-600")),un(r.Switch,"defaultColor",t("palette-grey-300")),un(r.Switch,"defaultDisabledColor",t("palette-grey-600")),un(r.Switch,"primaryDisabledColor",Xr(r.primary.main,.55)),un(r.Switch,"secondaryDisabledColor",Xr(r.secondary.main,.55)),un(r.Switch,"errorDisabledColor",Xr(r.error.main,.55)),un(r.Switch,"infoDisabledColor",Xr(r.info.main,.55)),un(r.Switch,"successDisabledColor",Xr(r.success.main,.55)),un(r.Switch,"warningDisabledColor",Xr(r.warning.main,.55)),un(r.TableCell,"border",Xr(qr(r.divider,1),.68)),un(r.Tooltip,"bg",qr(r.grey[700],.92))}fn(r.background,"default"),fn(r.background,"paper"),fn(r.common,"background"),fn(r.common,"onBackground"),fn(r,"divider"),Object.keys(r).forEach((e=>{const t=r[e];"tonalOffset"!==e&&t&&"object"==typeof t&&(t.main&&un(r[e],"mainChannel",zr(pn(t.main))),t.light&&un(r[e],"lightChannel",zr(pn(t.light))),t.dark&&un(r[e],"darkChannel",zr(pn(t.dark))),t.contrastText&&un(r[e],"contrastTextChannel",zr(pn(t.contrastText))),"text"===e&&(fn(r[e],"primary"),fn(r[e],"secondary")),"action"===e&&(t.active&&fn(r[e],"active"),t.selected&&fn(r[e],"selected")))}))})),S=r.reduce(((e,r)=>ke(e,r)),S);const $={prefix:i,disableCssColorScheme:o,shouldSkipGeneratingVar:a,getSelector:ln(S)},{vars:k,generateThemeVars:C,generateStyleSheets:O}=function(e,r={}){const{getSelector:t=g,disableCssColorScheme:n,colorSchemeSelector:o}=r,{colorSchemes:i={},components:a,defaultColorScheme:s="light",...c}=e,{vars:l,css:u,varsWithDefaults:p}=nt(c,r);let f=p;const d={},{[s]:m,...y}=i;if(Object.entries(y||{}).forEach((([e,t])=>{const{vars:n,css:o,varsWithDefaults:i}=nt(t,r);f=ke(f,i),d[e]={css:o,vars:n}})),m){const{css:e,vars:t,varsWithDefaults:n}=nt(m,r);f=ke(f,n),d[s]={css:e,vars:t}}function g(r,t){var n,a;let s=o;if("class"===o&&(s=".%s"),"data"===o&&(s="[data-%s]"),(null==o?void 0:o.startsWith("data-"))&&!o.includes("%s")&&(s=`[${o}="%s"]`),r){if("media"===s){if(e.defaultColorScheme===r)return":root";const o=(null==(a=null==(n=i[r])?void 0:n.palette)?void 0:a.mode)||r;return{[`@media (prefers-color-scheme: ${o})`]:{":root":t}}}if(s)return e.defaultColorScheme===r?`:root, ${s.replace("%s",String(r))}`:s.replace("%s",String(r))}return":root"}return{vars:f,generateThemeVars:()=>{let e={...l};return Object.entries(d).forEach((([,{vars:r}])=>{e=ke(e,r)})),e},generateStyleSheets:()=>{var r,o;const a=[],s=e.defaultColorScheme||"light";function c(e,r){Object.keys(r).length&&a.push("string"==typeof e?{[e]:{...r}}:e)}c(t(void 0,{...u}),u);const{[s]:l,...p}=d;if(l){const{css:e}=l,a=null==(o=null==(r=i[s])?void 0:r.palette)?void 0:o.mode,u=!n&&a?{colorScheme:a,...e}:{...e};c(t(s,{...u}),u)}return Object.entries(p).forEach((([e,{css:r}])=>{var o,a;const s=null==(a=null==(o=i[e])?void 0:o.palette)?void 0:a.mode,l=!n&&s?{colorScheme:s,...r}:{...r};c(t(e,{...l}),l)})),a}}}(S,$);return S.vars=k,Object.entries(S.colorSchemes[S.defaultColorScheme]).forEach((([e,r])=>{S[e]=r})),S.generateThemeVars=C,S.generateStyleSheets=O,S.generateSpacing=function(){return Xe(l.spacing,Ge(this))},S.getColorSchemeSelector=function(e){return function(r){return"media"===e?("production"!==process.env.NODE_ENV&&"light"!==r&&"dark"!==r&&console.error(`MUI: @media (prefers-color-scheme) supports only 'light' or 'dark', but receive '${r}'.`),`@media (prefers-color-scheme: ${r})`):e?e.startsWith("data-")&&!e.includes("%s")?`[${e}="${r}"] &`:"class"===e?`.${r} &`:"data"===e?`[data-${r}] &`:`${e.replace("%s",r)} &`:"&"}}(s),S.spacing=S.generateSpacing(),S.shouldSkipGeneratingVar=a,S.unstable_sxConfig={...Cr,...null==l?void 0:l.unstable_sxConfig},S.unstable_sx=function(e){return Or({sx:e,theme:this})},S.toRuntimeSource=tn,S}function gn(e,r,t){e.colorSchemes&&t&&(e.colorSchemes[r]={...!0!==t&&t,palette:Lt({...!0===t?{}:t.palette,mode:r})})}const hn=function(e={}){const{themeId:r,defaultTheme:t=Rr,rootShouldForwardProp:o=Mr,slotShouldForwardProp:i=Mr}=e;function a(e){!function(e,r,t){e.theme=function(e){for(const r in e)return!1;return!0}(e.theme)?t:e.theme[r]||e.theme}(e,r,t)}return(e,r={})=>{!function(e,r){Array.isArray(e.__emotion_styles)&&(e.__emotion_styles=r(e.__emotion_styles))}(e,(e=>e.filter((e=>e!==Or))));const{name:t,slot:s,skipVariantsResolver:c,skipSx:l,overridesResolver:u=Br(Wr(s)),...p}=r,f=void 0!==c?c:s&&"Root"!==s&&"root"!==s||!1,d=l||!1;let m=Mr;"Root"===s||"root"===s?m=o:s?m=i:function(e){return"string"==typeof e&&e.charCodeAt(0)>96}(e)&&(m=void 0);const y=function(e,r){const t=n(e,r);return"production"!==process.env.NODE_ENV?(...r)=>{const n="string"==typeof e?`"${e}"`:"component";return 0===r.length?console.error([`MUI: Seems like you called \`styled(${n})()\` without a \`style\` argument.`,'You must provide a `styles` argument: `styled("div")(styleYouForgotToPass)`.'].join("\n")):r.some((e=>void 0===e))&&console.error(`MUI: the styled(${n})(...args) API requires all its args to be defined.`),t(...r)}:t}(e,{shouldForwardProp:m,label:Fr(t,s),...p}),g=e=>{if(e.__emotion_real===e)return e;if("function"==typeof e)return function(r){return Dr(r,e)};if($e(e)){const r=function(e){const{variants:r,...t}=e,n={variants:r,style:pe(t),isProcessed:!0};return n.style===t||r&&r.forEach((e=>{"function"!=typeof e.style&&(e.style=pe(e.style))})),n}(e);return r.variants?function(e){return Dr(e,r)}:r.style}return e},h=(...r)=>{const n=[],o=r.map(g),i=[];if(n.push(a),t&&u&&i.push((function(e){var r,n;const o=null==(n=null==(r=e.theme.components)?void 0:r[t])?void 0:n.styleOverrides;if(!o)return null;const i={};for(const t in o)i[t]=Dr(e,o[t]);return u(e,i)})),t&&!f&&i.push((function(e){var r,n;const o=e.theme,i=null==(n=null==(r=null==o?void 0:o.components)?void 0:r[t])?void 0:n.variants;return i?Vr(e,i):null})),d||i.push(Or),Array.isArray(o[0])){const e=o.shift(),r=new Array(n.length).fill(""),t=new Array(i.length).fill("");let a;a=[...r,...e,...t],a.raw=[...r,...e.raw,...t],n.unshift(a)}const c=[...n,...o,...i],l=y(...c);return e.muiName&&(l.muiName=e.muiName),"production"!==process.env.NODE_ENV&&(l.displayName=function(e,r,t){if(e)return`${e}${Ie(r||"")}`;return`Styled(${function(e){if(null!=e){if("string"==typeof e)return e;if("function"==typeof e)return Ir(e,"Component");if("object"==typeof e)switch(e.$$typeof){case we.ForwardRef:return Pr(e,e.render,"ForwardRef");case we.Memo:return Pr(e,e.type,"memo");default:return}}}(t)})`}(t,s,e)),l};return y.withConfig&&(h.withConfig=y.withConfig),h}}({themeId:"$$material",defaultTheme:function(e={},...r){const{palette:t,cssVariables:n=!1,colorSchemes:o=(t?void 0:{light:!0}),defaultColorScheme:i=(null==t?void 0:t.mode),...a}=e,s=i||"light",c=null==o?void 0:o[s],l={...o,...t?{[s]:{..."boolean"!=typeof c&&c,palette:t}}:void 0};if(!1===n){if(!("colorSchemes"in e))return nn(e,...r);let n=t;"palette"in e||l[s]&&(!0!==l[s]?n=l[s].palette:"dark"===s&&(n={mode:"dark"}));const o=nn({...e,palette:n},...r);return o.defaultColorScheme=s,o.colorSchemes=l,"light"===o.palette.mode&&(o.colorSchemes.light={...!0!==l.light&&l.light,palette:o.palette},gn(o,"dark",l.dark)),"dark"===o.palette.mode&&(o.colorSchemes.dark={...!0!==l.dark&&l.dark,palette:o.palette},gn(o,"light",l.light)),o}return t||"light"in l||"light"!==s||(l.light=!0),yn({...a,colorSchemes:l,defaultColorScheme:s,..."boolean"!=typeof n&&n},...r)}(),rootShouldForwardProp:e=>function(e){return"ownerState"!==e&&"theme"!==e&&"sx"!==e&&"as"!==e}(e)&&"classes"!==e})(t.Paper)((({theme:e})=>({padding:e.spacing(4),textAlign:"center",background:"linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)",border:0,borderRadius:3,boxShadow:"0 3px 5px 2px rgba(255, 105, 135, .3)",color:"white",minHeight:200,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"})));e.HelloWorld=()=>{const e=$.c(4);let r,n,o,i;return e[0]===Symbol.for("react.memo_cache_sentinel")?(r={display:"flex",justifyContent:"center",alignItems:"center",minHeight:"50vh",p:2},e[0]=r):r=e[0],e[1]===Symbol.for("react.memo_cache_sentinel")?(n=y.jsx(t.Typography,{variant:"h3",component:"h1",gutterBottom:!0,children:"Hello World"}),o=y.jsx(t.Typography,{variant:"h6",component:"p",children:"Welcome to the React 19 Micro Frontend Template"}),e[1]=n,e[2]=o):(n=e[1],o=e[2]),e[3]===Symbol.for("react.memo_cache_sentinel")?(i=y.jsx(t.Box,{sx:r,children:y.jsxs(hn,{elevation:6,children:[n,o,y.jsx(t.Typography,{variant:"body1",component:"p",sx:{mt:2,opacity:.9},children:"This component is ready to be integrated into your micro frontend architecture"})]})}),e[3]=i):i=e[3],i},Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}));
//# sourceMappingURL=index.umd.js.map
