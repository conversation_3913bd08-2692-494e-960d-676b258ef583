# Deployment Guide

This guide covers different deployment strategies for the React 19 Micro Frontend Template.

## Table of Contents

- [NPM Package Deployment](#npm-package-deployment)
- [CDN Deployment](#cdn-deployment)
- [Module Federation Deployment](#module-federation-deployment)
- [Static Hosting Deployment](#static-hosting-deployment)
- [Container Deployment](#container-deployment)

## NPM Package Deployment

### Prerequisites

1. **NPM Account**: Create an account at [npmjs.com](https://www.npmjs.com)
2. **Authentication**: Login to npm locally
   ```bash
   npm login
   ```

### Publishing Process

1. **Update Version**
   ```bash
   npm version patch  # or minor/major
   ```

2. **Build Library**
   ```bash
   npm run build:lib
   ```

3. **Run Pre-publish Checks**
   ```bash
   npm run prepublishOnly
   ```

4. **Publish**
   ```bash
   npm publish
   ```

### Automated Publishing

The project includes GitHub Actions for automated publishing:

- Triggers on push to `main` branch
- Requires commit message to contain `[release]`
- Runs all tests and quality checks
- Publishes to NPM if all checks pass

### Package Configuration

Key package.json fields for publishing:

```json
{
  "name": "your-package-name",
  "version": "1.0.0",
  "main": "dist/index.js",
  "module": "dist/index.js",
  "types": "dist/index.d.ts",
  "files": ["dist"],
  "peerDependencies": {
    "react": "^19.0.0",
    "react-dom": "^19.0.0"
  }
}
```

## CDN Deployment

### Using unpkg

After publishing to NPM, your package is automatically available via CDN:

```html
<!-- UMD build -->
<script src="https://unpkg.com/your-package-name@latest/dist/index.umd.js"></script>

<!-- ES modules -->
<script type="module">
  import { HelloWorld } from 'https://unpkg.com/your-package-name@latest/dist/index.es.js'
</script>
```

### Using jsDelivr

```html
<!-- UMD build -->
<script src="https://cdn.jsdelivr.net/npm/your-package-name@latest/dist/index.umd.js"></script>

<!-- ES modules -->
<script type="module">
  import { HelloWorld } from 'https://cdn.jsdelivr.net/npm/your-package-name@latest/dist/index.es.js'
</script>
```

### Custom CDN

Deploy the `dist` folder to your preferred CDN:

1. **Build the library**
   ```bash
   npm run build:lib
   ```

2. **Upload dist folder** to your CDN
3. **Configure CORS** if needed
4. **Set cache headers** for optimal performance

## Module Federation Deployment

### Webpack Module Federation

1. **Configure webpack.config.js**
   ```javascript
   const ModuleFederationPlugin = require('@module-federation/webpack')
   
   module.exports = {
     plugins: [
       new ModuleFederationPlugin({
         name: 'insights_mfe',
         filename: 'remoteEntry.js',
         exposes: {
           './HelloWorld': './src/components/HelloWorld'
         },
         shared: {
           react: { singleton: true },
           'react-dom': { singleton: true },
           '@mui/material': { singleton: true }
         }
       })
     ]
   }
   ```

2. **Build and deploy**
   ```bash
   npm run build
   ```

3. **Host the build** on a web server

### Vite Module Federation

Using `@originjs/vite-plugin-federation`:

1. **Install plugin**
   ```bash
   npm install @originjs/vite-plugin-federation --save-dev
   ```

2. **Configure vite.config.ts**
   ```typescript
   import federation from '@originjs/vite-plugin-federation'
   
   export default defineConfig({
     plugins: [
       federation({
         name: 'insights-mfe',
         filename: 'remoteEntry.js',
         exposes: {
           './HelloWorld': './src/components/HelloWorld'
         },
         shared: ['react', 'react-dom', '@mui/material']
       })
     ]
   })
   ```

## Static Hosting Deployment

### Vercel

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy**
   ```bash
   vercel
   ```

3. **Configure vercel.json** (optional)
   ```json
   {
     "buildCommand": "npm run build",
     "outputDirectory": "dist",
     "framework": "vite"
   }
   ```

### Netlify

1. **Build the project**
   ```bash
   npm run build
   ```

2. **Deploy via CLI**
   ```bash
   npm install -g netlify-cli
   netlify deploy --prod --dir=dist
   ```

3. **Or use Git integration**
   - Connect your repository
   - Set build command: `npm run build`
   - Set publish directory: `dist`

### GitHub Pages

1. **Configure GitHub Actions**
   ```yaml
   # .github/workflows/deploy.yml
   name: Deploy to GitHub Pages
   
   on:
     push:
       branches: [ main ]
   
   jobs:
     deploy:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v4
         - uses: actions/setup-node@v4
           with:
             node-version: 20
             cache: 'npm'
         - run: npm ci
         - run: npm run build
         - uses: peaceiris/actions-gh-pages@v3
           with:
             github_token: ${{ secrets.GITHUB_TOKEN }}
             publish_dir: ./dist
   ```

### AWS S3 + CloudFront

1. **Build the project**
   ```bash
   npm run build
   ```

2. **Upload to S3**
   ```bash
   aws s3 sync dist/ s3://your-bucket-name --delete
   ```

3. **Configure CloudFront** for global CDN
4. **Set up Route 53** for custom domain (optional)

## Container Deployment

### Docker

1. **Create Dockerfile**
   ```dockerfile
   FROM node:20-alpine as builder
   
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci
   
   COPY . .
   RUN npm run build
   
   FROM nginx:alpine
   COPY --from=builder /app/dist /usr/share/nginx/html
   COPY nginx.conf /etc/nginx/nginx.conf
   
   EXPOSE 80
   CMD ["nginx", "-g", "daemon off;"]
   ```

2. **Create nginx.conf**
   ```nginx
   events {
     worker_connections 1024;
   }
   
   http {
     include /etc/nginx/mime.types;
     default_type application/octet-stream;
     
     server {
       listen 80;
       server_name localhost;
       
       location / {
         root /usr/share/nginx/html;
         index index.html index.htm;
         try_files $uri $uri/ /index.html;
       }
       
       # Enable gzip compression
       gzip on;
       gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
     }
   }
   ```

3. **Build and run**
   ```bash
   docker build -t insights-mfe .
   docker run -p 8080:80 insights-mfe
   ```

### Kubernetes

1. **Create deployment.yaml**
   ```yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: insights-mfe
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: insights-mfe
     template:
       metadata:
         labels:
           app: insights-mfe
       spec:
         containers:
         - name: insights-mfe
           image: your-registry/insights-mfe:latest
           ports:
           - containerPort: 80
   ---
   apiVersion: v1
   kind: Service
   metadata:
     name: insights-mfe-service
   spec:
     selector:
       app: insights-mfe
     ports:
     - port: 80
       targetPort: 80
     type: LoadBalancer
   ```

2. **Deploy**
   ```bash
   kubectl apply -f deployment.yaml
   ```

## Environment Configuration

### Environment Variables

Create environment-specific configurations:

```typescript
// src/config/environment.ts
export const config = {
  apiUrl: process.env.VITE_API_URL || 'http://localhost:3001',
  environment: process.env.NODE_ENV || 'development',
  version: process.env.VITE_APP_VERSION || '1.0.0'
}
```

### Build-time Configuration

Use Vite's environment variables:

```bash
# .env.production
VITE_API_URL=https://api.production.com
VITE_APP_VERSION=1.0.0
```

## Performance Optimization

### Bundle Optimization

1. **Analyze bundle size**
   ```bash
   npm install -g webpack-bundle-analyzer
   npx webpack-bundle-analyzer dist/stats.json
   ```

2. **Enable compression**
   - Gzip compression on server
   - Brotli compression for modern browsers

3. **Set cache headers**
   ```nginx
   location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
     expires 1y;
     add_header Cache-Control "public, immutable";
   }
   ```

### CDN Configuration

1. **Use appropriate cache headers**
2. **Enable compression**
3. **Configure geographic distribution**
4. **Set up monitoring and analytics**

## Monitoring and Analytics

### Error Tracking

Integrate error tracking services:

```typescript
// src/utils/errorTracking.ts
import * as Sentry from '@sentry/react'

Sentry.init({
  dsn: process.env.VITE_SENTRY_DSN,
  environment: process.env.NODE_ENV
})
```

### Performance Monitoring

```typescript
// src/utils/analytics.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

getCLS(console.log)
getFID(console.log)
getFCP(console.log)
getLCP(console.log)
getTTFB(console.log)
```

## Security Considerations

### Content Security Policy

```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src fonts.gstatic.com;">
```

### HTTPS Configuration

Always deploy with HTTPS in production:

1. **Use SSL certificates**
2. **Configure HSTS headers**
3. **Redirect HTTP to HTTPS**

### Dependency Security

```bash
# Audit dependencies
npm audit

# Fix vulnerabilities
npm audit fix
```

## Rollback Strategy

### Version Management

1. **Tag releases**
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```

2. **Keep previous versions available**
3. **Implement feature flags** for gradual rollouts

### Quick Rollback

1. **NPM**: Publish previous version
2. **CDN**: Update CDN to point to previous version
3. **Container**: Deploy previous container image

## Troubleshooting

### Common Deployment Issues

1. **Build failures**: Check dependencies and environment variables
2. **Runtime errors**: Verify peer dependencies are available
3. **Loading issues**: Check CORS configuration
4. **Performance issues**: Analyze bundle size and network requests

### Debugging Production Issues

1. **Enable source maps** for debugging
2. **Use error tracking** services
3. **Monitor performance** metrics
4. **Set up logging** and alerting
