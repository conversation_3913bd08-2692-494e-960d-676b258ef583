# Development Guide

This guide covers the development workflow for the React 19 Micro Frontend Template.

## Prerequisites

- Node.js 18+ (recommended: 20+)
- npm, yarn, or pnpm
- Git

## Development Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd insights-app-mfe-template
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open browser**
   Navigate to `http://localhost:3000`

## Development Workflow

### Code Quality

Before committing code, ensure it passes all quality checks:

```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Fix linting issues automatically
npm run lint:fix

# Format code
npm run format

# Run tests
npm run test

# Run tests with coverage
npm run test:coverage
```

### Building

```bash
# Build for development/preview
npm run build

# Build as library for npm publishing
npm run build:lib

# Preview production build
npm run preview
```

### Testing Strategy

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions
3. **Type Tests**: Ensure TypeScript types are correct

#### Writing Tests

- Place test files next to the components they test
- Use the `__tests__` directory for test files
- Follow the naming convention: `ComponentName.test.tsx`
- Mock external dependencies appropriately

Example test structure:
```typescript
describe('ComponentName', () => {
  test('renders correctly', () => {
    // Test implementation
  })
  
  test('handles user interactions', () => {
    // Test implementation
  })
})
```

## Project Structure

```
├── src/
│   ├── components/          # Reusable components
│   │   ├── HelloWorld.tsx   # Main MFE component
│   │   └── __tests__/       # Component tests
│   ├── main.tsx            # Development entry point
│   ├── bootstrap.tsx       # Development bootstrapping
│   ├── App.tsx            # Development app wrapper
│   ├── index.ts           # Library export entry
│   └── setupTests.ts      # Test configuration
├── examples/              # Usage examples
├── docs/                 # Documentation
├── .github/              # GitHub workflows and templates
└── dist/                 # Build output (generated)
```

## Component Development

### Creating New Components

1. **Create the component file**
   ```typescript
   // src/components/MyComponent.tsx
   import React from 'react'
   import { Box } from '@mui/material'
   
   interface MyComponentProps {
     title?: string
   }
   
   const MyComponent: React.FC<MyComponentProps> = ({ title }) => {
     return <Box>{title}</Box>
   }
   
   export default MyComponent
   ```

2. **Export from index.ts**
   ```typescript
   // src/index.ts
   export { default as MyComponent } from './components/MyComponent'
   ```

3. **Add tests**
   ```typescript
   // src/components/__tests__/MyComponent.test.tsx
   import React from 'react'
   import { render, screen } from '@testing-library/react'
   import MyComponent from '../MyComponent'
   
   test('renders component', () => {
     render(<MyComponent title="Test" />)
     expect(screen.getByText('Test')).toBeInTheDocument()
   })
   ```

### Material UI Guidelines

- Use Material UI components consistently
- Follow Material Design principles
- Use the theme system for styling
- Prefer styled components over inline styles
- Use responsive design patterns

### TypeScript Guidelines

- Define interfaces for all props
- Use strict typing
- Avoid `any` type
- Export types for consumers
- Use generic types where appropriate

## Build Configuration

### Vite Configuration

The project uses Vite with two build modes:

1. **Development Mode**: Standard build for local development
2. **Library Mode**: Optimized build for npm publishing

Key configuration points:
- Externalized peer dependencies
- TypeScript declaration generation
- Source maps for debugging
- Minification for production

### TypeScript Configuration

- Strict mode enabled
- Path mapping for clean imports
- Separate configs for source and config files
- Declaration file generation

## Debugging

### Development Debugging

1. **Browser DevTools**: Use React DevTools extension
2. **Console Logging**: Use `console.log` for quick debugging
3. **Breakpoints**: Set breakpoints in browser DevTools
4. **React DevTools**: Inspect component state and props

### Test Debugging

1. **Jest Debug Mode**: Run tests with `--verbose` flag
2. **Test Coverage**: Use coverage reports to find untested code
3. **Test Watch Mode**: Use `npm run test:watch` for continuous testing

### Build Debugging

1. **Bundle Analysis**: Analyze bundle size and dependencies
2. **Source Maps**: Use source maps for production debugging
3. **Build Logs**: Check build output for warnings and errors

## Performance Considerations

### Bundle Size

- Monitor bundle size with each change
- Use tree shaking to eliminate unused code
- Externalize large dependencies
- Use dynamic imports for code splitting

### Runtime Performance

- Use React.memo for expensive components
- Implement proper key props for lists
- Avoid unnecessary re-renders
- Use React DevTools Profiler

### Loading Performance

- Implement lazy loading where appropriate
- Optimize images and assets
- Use proper caching strategies
- Minimize initial bundle size

## Troubleshooting

### Common Issues

1. **Type Errors**: Check TypeScript configuration and imports
2. **Build Failures**: Verify all dependencies are installed
3. **Test Failures**: Check test setup and mocks
4. **Linting Errors**: Run `npm run lint:fix` to auto-fix issues

### Getting Help

1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information
4. Include error messages and steps to reproduce

## Contributing

See the main README.md for contribution guidelines.
