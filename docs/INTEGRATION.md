# Integration Guide

This guide explains how to integrate the React 19 Micro Frontend Template into different host applications and architectures.

## Table of Contents

- [NPM Package Integration](#npm-package-integration)
- [Module Federation Integration](#module-federation-integration)
- [Dynamic Import Integration](#dynamic-import-integration)
- [CDN Integration](#cdn-integration)
- [Theme Integration](#theme-integration)
- [State Management Integration](#state-management-integration)
- [Routing Integration](#routing-integration)

## NPM Package Integration

### Basic Integration

1. **Install the package**
   ```bash
   npm install insights-app-mfe-template
   ```

2. **Import and use components**
   ```typescript
   import React from 'react'
   import { HelloWorld } from 'insights-app-mfe-template'
   import { ThemeProvider, createTheme } from '@mui/material/styles'
   
   const theme = createTheme({
     palette: {
       primary: { main: '#1976d2' }
     }
   })
   
   function App() {
     return (
       <ThemeProvider theme={theme}>
         <div>
           <h1>Host Application</h1>
           <HelloWorld />
         </div>
       </ThemeProvider>
     )
   }
   ```

### TypeScript Integration

```typescript
import React from 'react'
import { HelloWorld } from 'insights-app-mfe-template'

// TypeScript will automatically infer types
function App() {
  return (
    <div>
      <HelloWorld />
    </div>
  )
}
```

### Peer Dependencies

Ensure peer dependencies are installed in your host application:

```json
{
  "dependencies": {
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "@mui/material": "^6.4.1",
    "@emotion/react": "^11.13.3",
    "@emotion/styled": "^11.13.0"
  }
}
```

## Module Federation Integration

### Webpack Module Federation

1. **Configure the micro frontend as a remote**
   ```javascript
   // webpack.config.js in micro frontend
   const ModuleFederationPlugin = require('@module-federation/webpack')
   
   module.exports = {
     plugins: [
       new ModuleFederationPlugin({
         name: 'insights_mfe',
         filename: 'remoteEntry.js',
         exposes: {
           './HelloWorld': './src/components/HelloWorld'
         },
         shared: {
           react: { singleton: true, requiredVersion: '^19.0.0' },
           'react-dom': { singleton: true, requiredVersion: '^19.0.0' },
           '@mui/material': { singleton: true }
         }
       })
     ]
   }
   ```

2. **Configure the host application**
   ```javascript
   // webpack.config.js in host application
   const ModuleFederationPlugin = require('@module-federation/webpack')
   
   module.exports = {
     plugins: [
       new ModuleFederationPlugin({
         name: 'host',
         remotes: {
           'insights-mfe': 'insights_mfe@http://localhost:3000/remoteEntry.js'
         },
         shared: {
           react: { singleton: true, requiredVersion: '^19.0.0' },
           'react-dom': { singleton: true, requiredVersion: '^19.0.0' },
           '@mui/material': { singleton: true }
         }
       })
     ]
   }
   ```

3. **Use in React component**
   ```typescript
   import React, { Suspense } from 'react'
   
   const RemoteHelloWorld = React.lazy(() => import('insights-mfe/HelloWorld'))
   
   function App() {
     return (
       <div>
         <h1>Host Application</h1>
         <Suspense fallback={<div>Loading micro frontend...</div>}>
           <RemoteHelloWorld />
         </Suspense>
       </div>
     )
   }
   ```

### Vite Module Federation

1. **Install plugin**
   ```bash
   npm install @originjs/vite-plugin-federation --save-dev
   ```

2. **Configure micro frontend**
   ```typescript
   // vite.config.ts in micro frontend
   import federation from '@originjs/vite-plugin-federation'
   
   export default defineConfig({
     plugins: [
       federation({
         name: 'insights-mfe',
         filename: 'remoteEntry.js',
         exposes: {
           './HelloWorld': './src/components/HelloWorld'
         },
         shared: ['react', 'react-dom', '@mui/material']
       })
     ]
   })
   ```

3. **Configure host application**
   ```typescript
   // vite.config.ts in host application
   import federation from '@originjs/vite-plugin-federation'
   
   export default defineConfig({
     plugins: [
       federation({
         name: 'host',
         remotes: {
           'insights-mfe': 'http://localhost:3000/assets/remoteEntry.js'
         },
         shared: ['react', 'react-dom', '@mui/material']
       })
     ]
   })
   ```

## Dynamic Import Integration

### Runtime Loading

```typescript
import React, { Suspense, lazy } from 'react'

// Dynamically import the micro frontend
const HelloWorld = lazy(() => 
  import('insights-app-mfe-template').then(module => ({
    default: module.HelloWorld
  }))
)

function App() {
  return (
    <div>
      <h1>Host Application</h1>
      <Suspense fallback={<div>Loading micro frontend...</div>}>
        <HelloWorld />
      </Suspense>
    </div>
  )
}
```

### Conditional Loading

```typescript
import React, { useState, Suspense, lazy } from 'react'

const HelloWorld = lazy(() => import('insights-app-mfe-template'))

function App() {
  const [showMFE, setShowMFE] = useState(false)
  
  return (
    <div>
      <button onClick={() => setShowMFE(!showMFE)}>
        Toggle Micro Frontend
      </button>
      
      {showMFE && (
        <Suspense fallback={<div>Loading...</div>}>
          <HelloWorld />
        </Suspense>
      )}
    </div>
  )
}
```

## CDN Integration

### UMD Build Integration

```html
<!DOCTYPE html>
<html>
<head>
  <title>Host Application</title>
  <!-- Material UI dependencies -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
</head>
<body>
  <div id="root"></div>
  
  <!-- React dependencies -->
  <script crossorigin src="https://unpkg.com/react@19/umd/react.production.min.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@19/umd/react-dom.production.min.js"></script>
  
  <!-- Material UI dependencies -->
  <script crossorigin src="https://unpkg.com/@mui/material@6.4.1/umd/material-ui.production.min.js"></script>
  
  <!-- Micro frontend -->
  <script crossorigin src="https://unpkg.com/insights-app-mfe-template@latest/dist/index.umd.js"></script>
  
  <script>
    const { HelloWorld } = window.InsightsAppMFE
    
    function App() {
      return React.createElement('div', null, 
        React.createElement('h1', null, 'Host Application'),
        React.createElement(HelloWorld)
      )
    }
    
    ReactDOM.render(React.createElement(App), document.getElementById('root'))
  </script>
</body>
</html>
```

### ES Modules Integration

```html
<!DOCTYPE html>
<html>
<head>
  <title>Host Application</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
</head>
<body>
  <div id="root"></div>
  
  <script type="module">
    import React from 'https://unpkg.com/react@19/index.js'
    import ReactDOM from 'https://unpkg.com/react-dom@19/index.js'
    import { HelloWorld } from 'https://unpkg.com/insights-app-mfe-template@latest/dist/index.es.js'
    
    function App() {
      return React.createElement('div', null,
        React.createElement('h1', null, 'Host Application'),
        React.createElement(HelloWorld)
      )
    }
    
    ReactDOM.render(React.createElement(App), document.getElementById('root'))
  </script>
</body>
</html>
```

## Theme Integration

### Shared Theme Provider

```typescript
import React from 'react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'
import { HelloWorld } from 'insights-app-mfe-template'

// Create a shared theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif',
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
        },
      },
    },
  },
})

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <div>
        <h1>Host Application</h1>
        <HelloWorld />
      </div>
    </ThemeProvider>
  )
}
```

### Theme Customization

```typescript
import React from 'react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import { HelloWorld } from 'insights-app-mfe-template'

// Extend the default theme
const customTheme = createTheme({
  palette: {
    primary: {
      main: '#2196f3',
      light: '#64b5f6',
      dark: '#1976d2',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  shape: {
    borderRadius: 8,
  },
})

function App() {
  return (
    <ThemeProvider theme={customTheme}>
      <HelloWorld />
    </ThemeProvider>
  )
}
```

## State Management Integration

### Context API Integration

```typescript
import React, { createContext, useContext, useState } from 'react'
import { HelloWorld } from 'insights-app-mfe-template'

// Create shared context
interface AppContextType {
  user: { name: string; id: string } | null
  setUser: (user: { name: string; id: string } | null) => void
}

const AppContext = createContext<AppContextType | undefined>(undefined)

export const useAppContext = () => {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useAppContext must be used within AppProvider')
  }
  return context
}

function AppProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<{ name: string; id: string } | null>(null)
  
  return (
    <AppContext.Provider value={{ user, setUser }}>
      {children}
    </AppContext.Provider>
  )
}

function App() {
  return (
    <AppProvider>
      <div>
        <h1>Host Application</h1>
        <HelloWorld />
      </div>
    </AppProvider>
  )
}
```

### Redux Integration

```typescript
import React from 'react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { HelloWorld } from 'insights-app-mfe-template'

// Configure Redux store
const store = configureStore({
  reducer: {
    // Your reducers
  },
})

function App() {
  return (
    <Provider store={store}>
      <div>
        <h1>Host Application</h1>
        <HelloWorld />
      </div>
    </Provider>
  )
}
```

## Routing Integration

### React Router Integration

```typescript
import React from 'react'
import { BrowserRouter, Routes, Route, Link } from 'react-router-dom'
import { HelloWorld } from 'insights-app-mfe-template'

function App() {
  return (
    <BrowserRouter>
      <nav>
        <Link to="/">Home</Link>
        <Link to="/mfe">Micro Frontend</Link>
      </nav>
      
      <Routes>
        <Route path="/" element={<div>Home Page</div>} />
        <Route path="/mfe" element={<HelloWorld />} />
      </Routes>
    </BrowserRouter>
  )
}
```

### Nested Routing

```typescript
import React from 'react'
import { Routes, Route, Outlet } from 'react-router-dom'
import { HelloWorld } from 'insights-app-mfe-template'

function MicroFrontendLayout() {
  return (
    <div>
      <h2>Micro Frontend Section</h2>
      <Outlet />
    </div>
  )
}

function App() {
  return (
    <Routes>
      <Route path="/mfe" element={<MicroFrontendLayout />}>
        <Route index element={<HelloWorld />} />
        <Route path="hello" element={<HelloWorld />} />
      </Route>
    </Routes>
  )
}
```

## Error Handling

### Error Boundaries

```typescript
import React, { Component, ErrorInfo, ReactNode } from 'react'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

class MicroFrontendErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Micro frontend error:', error, errorInfo)
    // Log to error tracking service
  }

  render() {
    if (this.state.hasError) {
      return (
        <div>
          <h2>Something went wrong with the micro frontend.</h2>
          <details>
            {this.state.error?.message}
          </details>
        </div>
      )
    }

    return this.props.children
  }
}

// Usage
function App() {
  return (
    <MicroFrontendErrorBoundary>
      <HelloWorld />
    </MicroFrontendErrorBoundary>
  )
}
```

## Performance Optimization

### Lazy Loading with Preloading

```typescript
import React, { Suspense, lazy } from 'react'

// Preload the component
const HelloWorldPreload = () => import('insights-app-mfe-template')
const HelloWorld = lazy(HelloWorldPreload)

// Preload on hover
function App() {
  return (
    <div>
      <button 
        onMouseEnter={() => HelloWorldPreload()}
        onClick={() => setShowMFE(true)}
      >
        Load Micro Frontend
      </button>
      
      <Suspense fallback={<div>Loading...</div>}>
        <HelloWorld />
      </Suspense>
    </div>
  )
}
```

### Bundle Splitting

```typescript
// Use dynamic imports for better code splitting
const loadMicroFrontend = async () => {
  const { HelloWorld } = await import('insights-app-mfe-template')
  return HelloWorld
}
```

## Testing Integration

### Testing with Host Application

```typescript
import React from 'react'
import { render, screen } from '@testing-library/react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import { HelloWorld } from 'insights-app-mfe-template'

const theme = createTheme()

test('micro frontend integrates correctly', () => {
  render(
    <ThemeProvider theme={theme}>
      <HelloWorld />
    </ThemeProvider>
  )
  
  expect(screen.getByText('Hello World')).toBeInTheDocument()
})
```

## Troubleshooting

### Common Integration Issues

1. **Peer dependency conflicts**: Ensure compatible versions
2. **Theme conflicts**: Use consistent Material UI versions
3. **Bundle size issues**: Check for duplicate dependencies
4. **Runtime errors**: Verify all required providers are present

### Debug Mode

```typescript
// Enable debug mode for development
if (process.env.NODE_ENV === 'development') {
  console.log('Micro frontend loaded:', HelloWorld)
}
```
