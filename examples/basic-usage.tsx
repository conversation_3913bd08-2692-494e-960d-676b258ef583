/**
 * Example of how to use the micro frontend in a host application
 */
import React from 'react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'
import { HelloWorld } from 'insights-app-mfe-template'

// Create your custom theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
})

function HostApp() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <div>
        <h1>Host Application</h1>
        <p>This is the host application that includes the micro frontend:</p>
        
        {/* Include the micro frontend component */}
        <HelloWorld />
        
        <p>The micro frontend is seamlessly integrated!</p>
      </div>
    </ThemeProvider>
  )
}

export default HostApp
