/**
 * Example Webpack Module Federation configuration for host application
 */
const ModuleFederationPlugin = require('@module-federation/webpack')

module.exports = {
  mode: 'development',
  devServer: {
    port: 3001,
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'host',
      remotes: {
        // Remote micro frontend
        'insights-mfe': 'insights_mfe@http://localhost:3000/remoteEntry.js',
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: '^19.0.0',
        },
        'react-dom': {
          singleton: true,
          requiredVersion: '^19.0.0',
        },
        '@mui/material': {
          singleton: true,
          requiredVersion: '^6.4.1',
        },
        '@emotion/react': {
          singleton: true,
        },
        '@emotion/styled': {
          singleton: true,
        },
      },
    }),
  ],
}

/**
 * Usage in React component:
 * 
 * import React, { Suspense } from 'react'
 * 
 * const RemoteHelloWorld = React.lazy(() => import('insights-mfe/HelloWorld'))
 * 
 * function App() {
 *   return (
 *     <div>
 *       <h1>Host Application</h1>
 *       <Suspense fallback={<div>Loading micro frontend...</div>}>
 *         <RemoteHelloWorld />
 *       </Suspense>
 *     </div>
 *   )
 * }
 */
