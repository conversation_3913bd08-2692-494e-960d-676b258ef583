{"name": "insights-app-mfe-template", "version": "1.0.0", "description": "React 19 Micro Frontend Template with Material UI", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "vite", "build": "tsc && vite build", "build:lib": "tsc && vite build --mode library", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "prepublishOnly": "npm run test && npm run lint && npm run build:lib"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.4.1", "@mui/material": "^6.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^6.28.0"}, "devDependencies": {"@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.25.9", "@babel/preset-typescript": "^7.26.0", "@eslint/js": "^9.15.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.3.3", "babel-plugin-react-compiler": "^0.0.0-experimental-592953e-20240517", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.3.3", "terser": "5.40.0", "ts-jest": "^29.2.5", "typescript": "^5.6.3", "vite": "^6.0.1", "vite-plugin-dts": "^4.3.0"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "keywords": ["react", "micro-frontend", "mfe", "template", "material-ui", "typescript", "vite"], "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/your-org/insights-app-mfe-template.git"}, "bugs": {"url": "https://github.com/your-org/insights-app-mfe-template/issues"}, "homepage": "https://github.com/your-org/insights-app-mfe-template#readme"}