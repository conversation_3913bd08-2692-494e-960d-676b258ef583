/**
 * Loading Spinner component for micro frontend
 * Provides consistent loading states
 */
import React from 'react'
import { Box, CircularProgress, Typography } from '@mui/material'
import { styled } from '@mui/material/styles'

interface LoadingSpinnerProps {
  message?: string
  size?: number
  color?: 'primary' | 'secondary' | 'inherit'
  fullScreen?: boolean
}

const LoadingContainer = styled(Box, {
  shouldForwardProp: prop => prop !== 'fullScreen',
})<{ fullScreen?: boolean }>(({ theme, fullScreen }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(3),
  ...(fullScreen && {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    zIndex: theme.zIndex.modal,
  }),
}))

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  message = 'Loading...',
  size = 40,
  color = 'primary',
  fullScreen = false,
}) => {
  return (
    <LoadingContainer fullScreen={fullScreen}>
      <CircularProgress size={size} color={color} />
      {message && (
        <Typography
          variant='body2'
          color='textSecondary'
          sx={{ mt: 2, textAlign: 'center' }}
        >
          {message}
        </Typography>
      )}
    </LoadingContainer>
  )
}

export default LoadingSpinner
