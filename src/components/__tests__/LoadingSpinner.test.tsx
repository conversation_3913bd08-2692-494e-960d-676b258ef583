/**
 * Tests for LoadingSpinner component
 */
import React from 'react'
import { render, screen } from '@testing-library/react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import LoadingSpinner from '../LoadingSpinner'

const theme = createTheme()

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  )
}

describe('LoadingSpinner Component', () => {
  test('renders with default props', () => {
    renderWithTheme(<LoadingSpinner />)
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument()
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })

  test('renders with custom message', () => {
    renderWithTheme(<LoadingSpinner message="Please wait..." />)
    
    expect(screen.getByText('Please wait...')).toBeInTheDocument()
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument()
  })

  test('renders without message when empty string provided', () => {
    renderWithTheme(<LoadingSpinner message="" />)
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument()
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument()
  })

  test('renders with custom size', () => {
    renderWithTheme(<LoadingSpinner size={60} />)
    
    const progressbar = screen.getByRole('progressbar')
    expect(progressbar).toBeInTheDocument()
    // Note: Testing exact size would require more complex DOM inspection
  })

  test('renders with different colors', () => {
    const { rerender } = renderWithTheme(<LoadingSpinner color="primary" />)
    expect(screen.getByRole('progressbar')).toBeInTheDocument()
    
    rerender(
      <ThemeProvider theme={theme}>
        <LoadingSpinner color="secondary" />
      </ThemeProvider>
    )
    expect(screen.getByRole('progressbar')).toBeInTheDocument()
  })

  test('renders in fullscreen mode', () => {
    const { container } = renderWithTheme(<LoadingSpinner fullScreen />)
    
    // Check if the container has position fixed (fullscreen styling)
    const loadingContainer = container.firstChild as HTMLElement
    expect(loadingContainer).toBeInTheDocument()
  })

  test('renders with all custom props', () => {
    renderWithTheme(
      <LoadingSpinner 
        message="Custom loading message"
        size={50}
        color="secondary"
        fullScreen={true}
      />
    )
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument()
    expect(screen.getByText('Custom loading message')).toBeInTheDocument()
  })
})
