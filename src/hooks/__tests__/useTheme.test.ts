/**
 * Tests for useTheme hook
 */
import { renderHook } from '@testing-library/react'
import { useTheme } from '../useTheme'

// Mock the entire Material UI module
jest.mock('@mui/material/styles', () => ({
  useTheme: jest.fn(),
}))

jest.mock('@mui/material', () => ({
  useMediaQuery: jest.fn(),
}))

// eslint-disable-next-line @typescript-eslint/no-require-imports
const mockUseTheme = require('@mui/material/styles').useTheme as jest.Mock
// eslint-disable-next-line @typescript-eslint/no-require-imports
const mockUseMediaQuery = require('@mui/material').useMediaQuery as jest.Mock

const mockTheme = {
  palette: {
    mode: 'light',
    primary: { main: '#1976d2' },
    secondary: { main: '#dc004e' },
    error: { main: '#f44336' },
    warning: { main: '#ff9800' },
    info: { main: '#2196f3' },
    success: { main: '#4caf50' },
    background: { default: '#ffffff', paper: '#ffffff' },
    text: { primary: '#000000', secondary: '#666666' },
  },
  spacing: (factor: number) => `${factor * 8}px`,
  breakpoints: {
    down: jest.fn(),
    between: jest.fn(),
    up: jest.fn(),
  },
}

describe('useTheme Hook', () => {
  beforeEach(() => {
    mockUseMediaQuery.mockClear()
    mockUseTheme.mockClear()
    mockUseTheme.mockReturnValue(mockTheme)
  })

  test('returns theme object', () => {
    mockUseMediaQuery.mockReturnValue(false)

    const { result } = renderHook(() => useTheme())

    expect(result.current.theme).toBeDefined()
    expect(result.current.theme.palette.primary.main).toBe('#1976d2')
  })

  test('detects mobile breakpoint', () => {
    mockUseMediaQuery
      .mockReturnValueOnce(true) // isMobile
      .mockReturnValueOnce(false) // isTablet
      .mockReturnValueOnce(false) // isDesktop

    const { result } = renderHook(() => useTheme())

    expect(result.current.isMobile).toBe(true)
    expect(result.current.isTablet).toBe(false)
    expect(result.current.isDesktop).toBe(false)
  })

  test('detects tablet breakpoint', () => {
    mockUseMediaQuery
      .mockReturnValueOnce(false) // isMobile
      .mockReturnValueOnce(true) // isTablet
      .mockReturnValueOnce(false) // isDesktop

    const { result } = renderHook(() => useTheme())

    expect(result.current.isMobile).toBe(false)
    expect(result.current.isTablet).toBe(true)
    expect(result.current.isDesktop).toBe(false)
  })

  test('detects desktop breakpoint', () => {
    mockUseMediaQuery
      .mockReturnValueOnce(false) // isMobile
      .mockReturnValueOnce(false) // isTablet
      .mockReturnValueOnce(true) // isDesktop

    const { result } = renderHook(() => useTheme())

    expect(result.current.isMobile).toBe(false)
    expect(result.current.isTablet).toBe(false)
    expect(result.current.isDesktop).toBe(true)
  })

  test('detects light mode', () => {
    mockUseMediaQuery.mockReturnValue(false)

    const { result } = renderHook(() => useTheme())

    expect(result.current.isDarkMode).toBe(false)
  })

  test('provides spacing values', () => {
    mockUseMediaQuery.mockReturnValue(false)

    const { result } = renderHook(() => useTheme())

    expect(result.current.spacing).toEqual({
      xs: '8px',
      sm: '16px',
      md: '24px',
      lg: '32px',
      xl: '48px',
    })
  })

  test('provides color values', () => {
    mockUseMediaQuery.mockReturnValue(false)

    const { result } = renderHook(() => useTheme())

    expect(result.current.colors.primary).toBe('#1976d2')
    expect(result.current.colors.secondary).toBe('#dc004e')
    expect(result.current.colors.text.primary).toBeDefined()
  })

  test('getBreakpointValue returns correct value for mobile', () => {
    mockUseMediaQuery
      .mockReturnValueOnce(true) // isMobile
      .mockReturnValueOnce(false) // isTablet
      .mockReturnValueOnce(false) // isDesktop

    const { result } = renderHook(() => useTheme())

    const value = result.current.getBreakpointValue({
      xs: 'mobile',
      sm: 'tablet',
      md: 'desktop',
    })

    expect(value).toBe('mobile')
  })

  test('getBreakpointValue returns correct value for tablet', () => {
    mockUseMediaQuery
      .mockReturnValueOnce(false) // isMobile
      .mockReturnValueOnce(true) // isTablet
      .mockReturnValueOnce(false) // isDesktop

    const { result } = renderHook(() => useTheme())

    const value = result.current.getBreakpointValue({
      xs: 'mobile',
      sm: 'tablet',
      md: 'desktop',
    })

    expect(value).toBe('tablet')
  })

  test('getBreakpointValue returns fallback value', () => {
    mockUseMediaQuery
      .mockReturnValueOnce(false) // isMobile
      .mockReturnValueOnce(false) // isTablet
      .mockReturnValueOnce(true) // isDesktop

    const { result } = renderHook(() => useTheme())

    const value = result.current.getBreakpointValue({
      xs: 'mobile',
      sm: 'tablet',
    })

    expect(value).toBe('mobile') // Falls back to first available
  })
})
