/**
 * Custom hook for theme utilities
 * Provides theme-related functionality for micro frontend components
 */
import { useTheme as useMuiTheme } from '@mui/material/styles'
import { useMediaQuery } from '@mui/material'

export const useTheme = () => {
  const theme = useMuiTheme()

  // Responsive breakpoints
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'))
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'))

  // Dark mode detection
  const isDarkMode = theme.palette.mode === 'dark'

  // Common spacing values
  const spacing = {
    xs: theme.spacing(1),
    sm: theme.spacing(2),
    md: theme.spacing(3),
    lg: theme.spacing(4),
    xl: theme.spacing(6),
  }

  // Common colors
  const colors = {
    primary: theme.palette.primary.main,
    secondary: theme.palette.secondary.main,
    error: theme.palette.error.main,
    warning: theme.palette.warning.main,
    info: theme.palette.info.main,
    success: theme.palette.success.main,
    background: theme.palette.background.default,
    paper: theme.palette.background.paper,
    text: {
      primary: theme.palette.text.primary,
      secondary: theme.palette.text.secondary,
    },
  }

  // Helper functions
  const getBreakpointValue = (values: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    xs?: any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    sm?: any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    md?: any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    lg?: any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    xl?: any
  }) => {
    if (isMobile && values.xs !== undefined) return values.xs
    if (isTablet && values.sm !== undefined) return values.sm
    if (isDesktop && values.md !== undefined) return values.md
    if (values.lg !== undefined) return values.lg
    if (values.xl !== undefined) return values.xl
    return values.xs || values.sm || values.md || values.lg || values.xl
  }

  return {
    theme,
    isMobile,
    isTablet,
    isDesktop,
    isDarkMode,
    spacing,
    colors,
    getBreakpointValue,
  }
}
