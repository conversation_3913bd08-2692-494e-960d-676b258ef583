/**
 * Logger utility for micro frontend
 * Provides consistent logging across the application
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

interface LogEntry {
  level: LogLevel
  message: string
  timestamp: Date
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  context?: Record<string, any>
  error?: Error
}

class Logger {
  private logLevel: LogLevel
  private prefix: string

  constructor(prefix = 'MFE', logLevel = LogLevel.INFO) {
    this.prefix = prefix
    this.logLevel =
      process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : logLevel
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel
  }

  private formatMessage(
    level: LogLevel,
    message: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    context?: Record<string, any>
  ): string {
    const timestamp = new Date().toISOString()
    const levelName = LogLevel[level]
    let formatted = `[${timestamp}] [${this.prefix}] [${levelName}] ${message}`

    if (context) {
      formatted += ` | Context: ${JSON.stringify(context)}`
    }

    return formatted
  }

  private log(
    level: LogLevel,
    message: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    context?: Record<string, any>,
    error?: Error
  ) {
    if (!this.shouldLog(level)) return

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context,
      error,
    }

    const formatted = this.formatMessage(level, message, context)

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formatted, error || '')
        break
      case LogLevel.INFO:
        console.info(formatted, error || '')
        break
      case LogLevel.WARN:
        console.warn(formatted, error || '')
        break
      case LogLevel.ERROR:
        console.error(formatted, error || '')
        break
    }

    // In production, you might want to send logs to a service
    if (process.env.NODE_ENV === 'production' && level >= LogLevel.ERROR) {
      this.sendToLoggingService(entry)
    }
  }

  private sendToLoggingService(entry: LogEntry) {
    // Implement your logging service integration here
    // Examples: Sentry, LogRocket, DataDog, etc.
    try {
      // Example: Send to external logging service
      // fetch('/api/logs', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(entry)
      // })

      // Prevent unused parameter warning
      void entry
    } catch (error) {
      console.error('Failed to send log to service:', error)
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  debug(message: string, context?: Record<string, any>) {
    this.log(LogLevel.DEBUG, message, context)
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  info(message: string, context?: Record<string, any>) {
    this.log(LogLevel.INFO, message, context)
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  warn(message: string, context?: Record<string, any>) {
    this.log(LogLevel.WARN, message, context)
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  error(message: string, error?: Error, context?: Record<string, any>) {
    this.log(LogLevel.ERROR, message, context, error)
  }

  // Performance logging
  time(label: string) {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.time(`${this.prefix}:${label}`)
    }
  }

  timeEnd(label: string) {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.timeEnd(`${this.prefix}:${label}`)
    }
  }

  // Group logging
  group(label: string) {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.group(`${this.prefix}:${label}`)
    }
  }

  groupEnd() {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.groupEnd()
    }
  }
}

// Create default logger instance
export const logger = new Logger('InsightsMFE')

// Export Logger class for custom instances
export { Logger }

// Convenience functions
export const log = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  debug: (message: string, context?: Record<string, any>) =>
    logger.debug(message, context),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  info: (message: string, context?: Record<string, any>) =>
    logger.info(message, context),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  warn: (message: string, context?: Record<string, any>) =>
    logger.warn(message, context),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  error: (message: string, error?: Error, context?: Record<string, any>) =>
    logger.error(message, error, context),
  time: (label: string) => logger.time(label),
  timeEnd: (label: string) => logger.timeEnd(label),
  group: (label: string) => logger.group(label),
  groupEnd: () => logger.groupEnd(),
}
