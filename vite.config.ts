import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isLibrary = mode === 'library'

  return {
    plugins: [
      react({
        babel: {
          plugins: [
            // React Compiler plugin for React 19 optimizations
            ['babel-plugin-react-compiler', {}],
          ],
        },
      }),
      // Generate TypeScript declaration files for library build
      ...(isLibrary
        ? [
            dts({
              insertTypesEntry: true,
              include: ['src/index.ts', 'src/components/**/*'],
              exclude: ['src/main.tsx', 'src/bootstrap.tsx', 'src/App.tsx'],
            }),
          ]
        : []),
    ],

    // Development server configuration
    server: {
      port: 3000,
      open: true,
      cors: true,
    },

    // Build configuration
    build: isLibrary
      ? {
          // Library build configuration for micro frontend
          lib: {
            entry: resolve(__dirname, 'src/index.ts'),
            name: 'InsightsAppMFE',
            formats: ['es', 'umd'],
            fileName: format => `index.${format}.js`,
          },
          rollupOptions: {
            // Externalize peer dependencies to avoid bundling them
            external: [
              'react',
              'react-dom',
              'react-router-dom',
              '@mui/material',
              '@mui/icons-material',
              '@emotion/react',
              '@emotion/styled',
            ],
            output: {
              exports: 'named',
              globals: {
                react: 'React',
                'react-dom': 'ReactDOM',
                'react-router-dom': 'ReactRouterDOM',
                '@mui/material': 'MaterialUI',
                '@mui/icons-material': 'MaterialUIIcons',
                '@emotion/react': 'EmotionReact',
                '@emotion/styled': 'EmotionStyled',
              },
            },
          },
          sourcemap: true,
          minify: 'terser',
        }
      : {
          // Standard build for development/preview
          outDir: 'dist',
          sourcemap: true,
          minify: 'terser',
          rollupOptions: {
            output: {
              manualChunks: {
                vendor: ['react', 'react-dom'],
                mui: [
                  '@mui/material',
                  '@mui/icons-material',
                  '@emotion/react',
                  '@emotion/styled',
                ],
                router: ['react-router-dom'],
              },
            },
          },
        },

    // Resolve configuration
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },

    // Define global constants
    define: {
      __DEV__: JSON.stringify(mode === 'development'),
    },
  }
})
